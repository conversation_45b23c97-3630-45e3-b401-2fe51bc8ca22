// Mock数据接口
const mockRadioList = [
  {
    id: 1,
    name: '英文儿歌',
    description: '精选英文儿歌，帮助孩子学习英语',
    cover_url: 'https://img.zcool.cn/community/01b72d5d145777a8012051cdb5458b.jpg',
    cover_file_id: 1001,
    language: '1',
    order: 1,
    status: '1',
    audio_num: 12,
    play_times: 1560,
    deleted: '0',
    created: '2023-05-15 10:00:00',
    updated: '2023-06-20 15:30:00',
    play_stram_url: 'https://www.soundhelix.com/examples/mp3/stream',
    audioList: [
      {
        id: 101,
        name: 'Twinkle Twinkle Little Star',
        file_url: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-1.mp3',
        file_id: 10101,
        seconds: 125,
        status: '1',
        deleted: '0',
        created: '2023-05-15 10:00:00',
        updated: '2023-06-20 15:30:00'
      },
      {
        id: 102,
        name: '<PERSON> Song',
        file_url: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-2.mp3',
        file_id: 10102,
        seconds: 98,
        status: '1',
        deleted: '0',
        created: '2023-05-15 10:00:00',
        updated: '2023-06-20 15:30:00'
      },
      {
        id: 103,
        name: 'Old MacDonald Had a Farm',
        file_url: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-3.mp3',
        file_id: 10103,
        seconds: 142,
        status: '1',
        deleted: '0',
        created: '2023-05-15 10:00:00',
        updated: '2023-06-20 15:30:00'
      }
    ]
  },
  {
    id: 2,
    name: '中文儿歌',
    description: '经典中文儿歌合集',
    cover_url: 'https://img.zcool.cn/community/01f0de5d145777a8012051cdb54590.jpg',
    cover_file_id: 1002,
    language: '0',
    order: 2,
    status: '1',
    audio_num: 10,
    play_times: 2340,
    deleted: '0',
    created: '2023-04-10 09:15:00',
    updated: '2023-06-18 11:20:00',
    play_stram_url: 'https://www.soundhelix.com/examples/mp3/stream',
    audioList: [
      {
        id: 201,
        name: '两只老虎',
        file_url: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-4.mp3',
        file_id: 10201,
        seconds: 85,
        status: '1',
        deleted: '0',
        created: '2023-05-15 10:00:00',
        updated: '2023-06-20 15:30:00'
      },
      {
        id: 202,
        name: '小燕子',
        file_url: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-5.mp3',
        file_id: 10202,
        seconds: 112,
        status: '1',
        deleted: '0',
        created: '2023-05-15 10:00:00',
        updated: '2023-06-20 15:30:00'
      }
    ]
  },
  {
    id: 3,
    name: '英语启蒙',
    description: '针对3-6岁儿童的英语启蒙课程',
    cover_url: 'https://img.zcool.cn/community/01e4ce5d145777a8012051cdb545a2.jpg',
    cover_file_id: 1003,
    language: '1',
    order: 3,
    status: '1',
    audio_num: 8,
    play_times: 1890,
    deleted: '0',
    created: '2023-03-20 14:30:00',
    updated: '2023-06-15 16:45:00',
    play_stram_url: 'https://www.soundhelix.com/examples/mp3/stream',
    audioList: [
      {
        id: 301,
        name: 'Hello Song',
        file_url: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-6.mp3',
        file_id: 10301,
        seconds: 95,
        status: '1',
        deleted: '0',
        created: '2023-03-20 14:35:00',
        updated: '2023-06-15 16:50:00'
      },
      {
        id: 302,
        name: 'Colors Song',
        file_url: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-7.mp3',
        file_id: 10302,
        seconds: 105,
        status: '1',
        deleted: '0',
        created: '2023-03-20 14:40:00',
        updated: '2023-06-15 16:55:00'
      }
    ]
  },
  {
    id: 4,
    name: '睡前故事',
    description: '帮助孩子安静入睡的睡前故事',
    cover_url: 'https://img.zcool.cn/community/01f3d65d145777a8012051cdb545b0.jpg',
    cover_file_id: 1004,
    language: '0',
    order: 4,
    status: '1',
    audio_num: 15,
    play_times: 3200,
    deleted: '0',
    created: '2023-02-05 20:00:00',
    updated: '2023-06-10 21:15:00',
    play_stram_url: 'https://www.soundhelix.com/examples/mp3/stream',
    audioList: [
      {
        id: 401,
        name: '小红帽',
        file_url: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-8.mp3',
        file_id: 10401,
        seconds: 185,
        status: '1',
        deleted: '0',
        created: '2023-02-05 20:10:00',
        updated: '2023-06-10 21:20:00'
      },
      {
        id: 402,
        name: '三只小猪',
        file_url: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-9.mp3',
        file_id: 10402,
        seconds: 210,
        status: '1',
        deleted: '0',
        created: '2023-02-05 20:15:00',
        updated: '2023-06-10 21:25:00'
      }
    ]
  },
  {
    id: 5,
    name: '法语入门',
    description: '适合初学者的法语学习课程',
    cover_url: 'https://img.zcool.cn/community/01f3d65d145777a8012051cdb545b0.jpg',
    cover_file_id: 1005,
    language: '1',
    order: 5,
    status: '1',
    audio_num: 7,
    play_times: 980,
    deleted: '0',
    created: '2023-01-15 08:30:00',
    updated: '2023-05-25 14:20:00',
    play_stram_url: 'https://www.soundhelix.com/examples/mp3/stream',
    audioList: [
      {
        id: 501,
        name: 'Bonjour et Au Revoir',
        file_url: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-10.mp3',
        file_id: 10501,
        seconds: 115,
        status: '1',
        deleted: '0',
        created: '2023-01-15 08:35:00',
        updated: '2023-05-25 14:25:00'
      }
    ]
  },
  {
    id: 6,
    name: '日语会话',
    description: '日常日语会话练习',
    cover_url: 'https://img.zcool.cn/community/01e4ce5d145777a8012051cdb545a2.jpg',
    cover_file_id: 1006,
    language: '1',
    order: 6,
    status: '1',
    audio_num: 9,
    play_times: 1450,
    deleted: '0',
    created: '2023-01-10 09:45:00',
    updated: '2023-05-20 16:30:00',
    play_stram_url: 'https://www.soundhelix.com/examples/mp3/stream',
    audioList: [
      {
        id: 601,
        name: '自我介绍',
        file_url: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-11.mp3',
        file_id: 10601,
        seconds: 90,
        status: '1',
        deleted: '0',
        created: '2023-01-10 09:50:00',
        updated: '2023-05-20 16:35:00'
      }
    ]
  },
  {
    id: 7,
    name: '德语基础',
    description: '德语基础学习课程',
    cover_url: 'https://img.zcool.cn/community/01b72d5d145777a8012051cdb5458b.jpg',
    cover_file_id: 1007,
    language: '1',
    order: 7,
    status: '1',
    audio_num: 6,
    play_times: 760,
    deleted: '0',
    created: '2022-12-20 10:15:00',
    updated: '2023-05-15 11:40:00',
    play_stram_url: 'https://www.soundhelix.com/examples/mp3/stream',
    audioList: [
      {
        id: 701,
        name: 'Zahlen und Farben',
        file_url: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-12.mp3',
        file_id: 10701,
        seconds: 130,
        status: '1',
        deleted: '0',
        created: '2022-12-20 10:20:00',
        updated: '2023-05-15 11:45:00'
      }
    ]
  },
  {
    id: 8,
    name: '西班牙语会话',
    description: '日常西班牙语对话练习',
    cover_url: 'https://img.zcool.cn/community/01f0de5d145777a8012051cdb54590.jpg',
    cover_file_id: 1008,
    language: '1',
    order: 8,
    status: '1',
    audio_num: 8,
    play_times: 920,
    deleted: '0',
    created: '2022-12-15 14:20:00',
    updated: '2023-05-10 09:30:00',
    play_stram_url: 'https://www.soundhelix.com/examples/mp3/stream',
    audioList: [
      {
        id: 801,
        name: 'Saludos y Despedidas',
        file_url: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-13.mp3',
        file_id: 10801,
        seconds: 110,
        status: '1',
        deleted: '0',
        created: '2022-12-15 14:25:00',
        updated: '2023-05-10 09:35:00'
      }
    ]
  },
  {
    id: 9,
    name: '意大利语基础',
    description: '意大利语基础学习课程',
    cover_url: 'https://img.zcool.cn/community/01e4ce5d145777a8012051cdb545a2.jpg',
    cover_file_id: 1009,
    language: '1',
    order: 9,
    status: '1',
    audio_num: 5,
    play_times: 680,
    deleted: '0',
    created: '2022-12-10 16:45:00',
    updated: '2023-05-05 13:20:00',
    play_stram_url: 'https://www.soundhelix.com/examples/mp3/stream',
    audioList: [
      {
        id: 901,
        name: 'Numeri e Colori',
        file_url: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-14.mp3',
        file_id: 10901,
        seconds: 95,
        status: '1',
        deleted: '0',
        created: '2022-12-10 16:50:00',
        updated: '2023-05-05 13:25:00'
      }
    ]
  },
  {
    id: 10,
    name: '俄语入门',
    description: '俄语入门学习课程',
    cover_url: 'https://img.zcool.cn/community/01f3d65d145777a8012051cdb545b0.jpg',
    cover_file_id: 1010,
    language: '1',
    order: 10,
    status: '1',
    audio_num: 7,
    play_times: 540,
    deleted: '0',
    created: '2022-12-05 11:30:00',
    updated: '2023-05-01 10:15:00',
    play_stram_url: 'https://www.soundhelix.com/examples/mp3/stream',
    audioList: [
      {
        id: 1001,
        name: 'Приветствия и Прощания',
        file_url: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-15.mp3',
        file_id: 11001,
        seconds: 120,
        status: '1',
        deleted: '0',
        created: '2022-12-05 11:35:00',
        updated: '2023-05-01 10:20:00'
      }
    ]
  },
  {
    id: 11,
    name: '韩语基础',
    description: '韩语基础学习课程',
    cover_url: 'https://img.zcool.cn/community/01b72d5d145777a8012051cdb5458b.jpg',
    cover_file_id: 1011,
    language: '1',
    order: 11,
    status: '1',
    audio_num: 6,
    play_times: 820,
    deleted: '0',
    created: '2022-11-30 09:20:00',
    updated: '2023-04-25 15:40:00',
    play_stram_url: 'https://www.soundhelix.com/examples/mp3/stream',
    audioList: [
      {
        id: 1101,
        name: '안녕하세요 (你好)',
        file_url: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-16.mp3',
        file_id: 11101,
        seconds: 85,
        status: '1',
        deleted: '0',
        created: '2022-11-30 09:25:00',
        updated: '2023-04-25 15:45:00'
      }
    ]
  },
  {
    id: 12,
    name: '葡萄牙语入门',
    description: '葡萄牙语基础学习课程',
    cover_url: 'https://img.zcool.cn/community/01b72d5d145777a8012051cdb5458b.jpg',
    cover_file_id: 1012,
    language: '1',
    order: 12,
    status: '1',
    audio_num: 8,
    play_times: 650,
    deleted: '0',
    created: '2022-11-25 14:20:00',
    updated: '2023-04-20 16:30:00',
    play_stram_url: 'https://www.soundhelix.com/examples/mp3/stream',
    audioList: [
      {
        id: 1201,
        name: 'Olá e Adeus',
        file_url: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-17.mp3',
        file_id: 11201,
        seconds: 95,
        status: '1',
        deleted: '0',
        created: '2022-11-25 14:25:00',
        updated: '2023-04-20 16:35:00'
      }
    ]
  },
  {
    id: 13,
    name: '阿拉伯语基础',
    description: '阿拉伯语基础学习课程',
    cover_url: 'https://img.zcool.cn/community/01f3d65d145777a8012051cdb545b0.jpg',
    cover_file_id: 1013,
    language: '1',
    order: 13,
    status: '1',
    audio_num: 6,
    play_times: 420,
    deleted: '0',
    created: '2022-11-20 11:30:00',
    updated: '2023-04-15 13:45:00',
    play_stram_url: 'https://www.soundhelix.com/examples/mp3/stream',
    audioList: [
      {
        id: 1301,
        name: 'مرحبا وداعا (你好和再见)',
        file_url: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-18.mp3',
        file_id: 11301,
        seconds: 105,
        status: '1',
        deleted: '0',
        created: '2022-11-20 11:35:00',
        updated: '2023-04-15 13:50:00'
      }
    ]
  },
  {
    id: 14,
    name: '希腊语基础',
    description: '希腊语基础学习课程',
    cover_url: 'https://img.zcool.cn/community/01e4ce5d145777a8012051cdb545a2.jpg',
    cover_file_id: 1014,
    language: '1',
    order: 14,
    status: '1',
    audio_num: 5,
    play_times: 380,
    deleted: '0',
    created: '2022-11-15 09:25:00',
    updated: '2023-04-10 15:20:00',
    play_stram_url: 'https://www.soundhelix.com/examples/mp3/stream',
    audioList: [
      {
        id: 1401,
        name: 'Γεια σου και αντίο',
        file_url: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-19.mp3',
        file_id: 11401,
        seconds: 90,
        status: '1',
        deleted: '0',
        created: '2022-11-15 09:30:00',
        updated: '2023-04-10 15:25:00'
      }
    ]
  }
];

// Mock音频数据
const mockAudioList = [
  {
    id: 101,
    name: 'Twinkle Twinkle Little Star',
    radioId: 1,
    radioName: '英文儿歌',
    file_url: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-1.mp3',
    file_id: 10101,
    file_size: 2048000,
    seconds: 125,
    order: 1,
    play_count: 568,
    status: '1',
    deleted: '0',
    created: '2023-05-15 10:00:00',
    updated: '2023-06-20 15:30:00'
  },
  {
    id: 102,
    name: 'ABC Song',
    radioId: 1,
    radioName: '英文儿歌',
    file_url: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-2.mp3',
    file_id: 10102,
    file_size: 1536000,
    seconds: 98,
    order: 2,
    play_count: 432,
    status: '1',
    deleted: '0',
    created: '2023-05-15 10:00:00',
    updated: '2023-06-20 15:30:00'
  },
  {
    id: 103,
    name: 'Old MacDonald Had a Farm',
    radioId: 1,
    radioName: '英文儿歌',
    file_url: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-3.mp3',
    file_id: 10103,
    file_size: 2867200,
    seconds: 142,
    order: 3,
    play_count: 321,
    status: '1',
    deleted: '0',
    created: '2023-05-15 10:00:00',
    updated: '2023-06-20 15:30:00'
  },
  {
    id: 201,
    name: '两只老虎',
    radioId: 2,
    radioName: '中文儿歌',
    file_url: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-4.mp3',
    file_id: 10201,
    file_size: 1638400,
    seconds: 85,
    order: 1,
    play_count: 876,
    status: '1',
    deleted: '0',
    created: '2023-05-15 10:00:00',
    updated: '2023-06-20 15:30:00'
  },
  {
    id: 202,
    name: '小燕子',
    radioId: 2,
    radioName: '中文儿歌',
    file_url: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-5.mp3',
    file_id: 10202,
    file_size: 2252800,
    seconds: 112,
    order: 2,
    play_count: 654,
    status: '1',
    deleted: '0',
    created: '2023-05-15 10:00:00',
    updated: '2023-06-20 15:30:00'
  },
  {
    id: 301,
    name: 'Hello Song',
    radioId: 3,
    radioName: '英语启蒙',
    file_url: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-6.mp3',
    file_id: 10301,
    file_size: 1408000,
    seconds: 95,
    order: 1,
    play_count: 540,
    status: '1',
    deleted: '0',
    created: '2023-03-20 14:35:00',
    updated: '2023-06-15 16:50:00'
  },
  {
    id: 302,
    name: 'Colors Song',
    radioId: 3,
    radioName: '英语启蒙',
    file_url: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-7.mp3',
    file_id: 10302,
    file_size: 1664000,
    seconds: 105,
    order: 2,
    play_count: 490,
    status: '1',
    deleted: '0',
    created: '2023-03-20 14:40:00',
    updated: '2023-06-15 16:55:00'
  },
  {
    id: 401,
    name: '小红帽',
    radioId: 4,
    radioName: '睡前故事',
    file_url: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-8.mp3',
    file_id: 10401,
    file_size: 3072000,
    seconds: 185,
    order: 1,
    play_count: 830,
    status: '1',
    deleted: '0',
    created: '2023-02-05 20:10:00',
    updated: '2023-06-10 21:20:00'
  },
  {
    id: 402,
    name: '三只小猪',
    radioId: 4,
    radioName: '睡前故事',
    file_url: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-9.mp3',
    file_id: 10402,
    file_size: 3584000,
    seconds: 210,
    order: 2,
    play_count: 760,
    status: '1',
    deleted: '0',
    created: '2023-02-05 20:15:00',
    updated: '2023-06-10 21:25:00'
  },
  {
    id: 501,
    name: 'Bonjour et Au Revoir',
    radioId: 5,
    radioName: '法语入门',
    file_url: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-10.mp3',
    file_id: 10501,
    file_size: 1920000,
    seconds: 115,
    order: 1,
    play_count: 320,
    status: '1',
    deleted: '0',
    created: '2023-01-15 08:35:00',
    updated: '2023-05-25 14:25:00'
  },
  {
    id: 601,
    name: '自我介绍',
    radioId: 6,
    radioName: '日语会话',
    file_url: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-11.mp3',
    file_id: 10601,
    file_size: 1536000,
    seconds: 90,
    order: 1,
    play_count: 410,
    status: '1',
    deleted: '0',
    created: '2023-01-10 09:50:00',
    updated: '2023-05-20 16:35:00'
  },
  {
    id: 701,
    name: 'Zahlen und Farben',
    radioId: 7,
    radioName: '德语基础',
    file_url: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-12.mp3',
    file_id: 10701,
    file_size: 2304000,
    seconds: 130,
    order: 1,
    play_count: 280,
    status: '1',
    deleted: '0',
    created: '2022-12-20 10:20:00',
    updated: '2023-05-15 11:45:00'
  },
  {
    id: 1201,
    name: 'Olá e Adeus',
    radioId: 12,
    radioName: '葡萄牙语入门',
    file_url: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-17.mp3',
    file_id: 11201,
    file_size: 1792000,
    seconds: 95,
    order: 1,
    play_count: 210,
    status: '1',
    deleted: '0',
    created: '2022-11-25 14:25:00',
    updated: '2023-04-20 16:35:00'
  },
  {
    id: 1301,
    name: 'مرحبا وداعا (你好和再见)',
    radioId: 13,
    radioName: '阿拉伯语基础',
    file_url: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-18.mp3',
    file_id: 11301,
    file_size: 1664000,
    seconds: 105,
    order: 1,
    play_count: 180,
    status: '1',
    deleted: '0',
    created: '2022-11-20 11:35:00',
    updated: '2023-04-15 13:50:00'
  },
  {
    id: 1401,
    name: 'Γεια σου και αντίο',
    radioId: 14,
    radioName: '希腊语基础',
    file_url: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-19.mp3',
    file_id: 11401,
    file_size: 1536000,
    seconds: 90,
    order: 1,
    play_count: 150,
    status: '1',
    deleted: '0',
    created: '2022-11-15 09:30:00',
    updated: '2023-04-10 15:25:00'
  },
  {
    id: 104,
    name: 'Baa Baa Black Sheep',
    radioId: 1,
    radioName: '英文儿歌',
    file_url: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-20.mp3',
    file_id: 10104,
    file_size: 1792000,
    seconds: 110,
    order: 4,
    play_count: 385,
    status: '1',
    deleted: '0',
    created: '2023-05-15 10:45:00',
    updated: '2023-06-20 15:50:00'
  },
  {
    id: 105,
    name: 'Itsy Bitsy Spider',
    radioId: 1,
    radioName: '英文儿歌',
    file_url: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-21.mp3',
    file_id: 10105,
    file_size: 1920000,
    seconds: 115,
    order: 5,
    play_count: 420,
    status: '1',
    deleted: '0',
    created: '2023-05-15 10:50:00',
    updated: '2023-06-20 15:55:00'
  }
];

// 分页功能
function mockPagination(list, query) {
  const { pageNum = 1, pageSize = 10 } = query;
  const total = list.length;
  const pages = Math.ceil(total / pageSize);
  const start = (pageNum - 1) * pageSize;
  const end = pageNum * pageSize;
  const rows = list.slice(start, end);
  return {
    code: 200,
    msg: "查询成功",
    rows,
    total,
    pageNum: Number(pageNum),
    pageSize: Number(pageSize),
    pages
  };
}

// 过滤功能
function mockFilter(list, query) {
  let result = [...list];
  
  // 电台ID
  if (query.radioId) {
    result = result.filter(item => item.radioId === Number(query.radioId));
  }
  
  // 电台名称
  if (query.name) {
    result = result.filter(item => item.name.includes(query.name));
  }
  
  // 语种分类
  if (query.language) {
    result = result.filter(item => item.language === query.language);
  }
  
  // 状态
  if (query.status) {
    result = result.filter(item => item.status === query.status);
  }
  
  // 音频ID
  if (query.id) {
    result = result.filter(item => item.id === Number(query.id));
  }
  
  // 创建时间范围
  if (query.params && query.params.beginTime && query.params.endTime) {
    const beginTime = new Date(query.params.beginTime).getTime();
    const endTime = new Date(query.params.endTime).getTime();
    result = result.filter(item => {
      const time = new Date(item.created).getTime();
      return time >= beginTime && time <= endTime;
    });
  }
  
  return result;
}

// 获取电台列表
export function getMockRadioList(query = {}) {
  const filteredList = mockFilter(mockRadioList, query);
  return Promise.resolve(mockPagination(filteredList, query));
}

// 获取电台详情
export function getMockRadio(radioId) {
  const radio = mockRadioList.find(item => item.id === Number(radioId));
  return Promise.resolve({
    code: 200,
    msg: "查询成功",
    data: radio
  });
}

// 获取音频列表
export function getMockAudioList(query = {}) {
  const filteredList = mockFilter(mockAudioList, query);
  return Promise.resolve(mockPagination(filteredList, query));
}

// 获取音频详情
export function getMockAudio(audioId) {
  const audio = mockAudioList.find(item => item.id === Number(audioId));
  return Promise.resolve({
    code: 200,
    msg: "查询成功",
    data: audio
  });
}

// 获取电台下的音频列表
export function getMockRadioAudios(radioId) {
  // 获取匹配的电台
  const radio = mockRadioList.find(item => item.id === Number(radioId));
  
  // 如果找到电台并且有音频列表，则返回它
  if (radio && radio.audioList && radio.audioList.length > 0) {
    // 为每个音频添加一些额外信息
    const audioList = radio.audioList.map(audio => {
      return {
        ...audio,
        radioId: radio.id,
        radioName: radio.name,
        audioName: audio.name,
        audioUrl: audio.file_url,
        play_count: audio.play_count || 0
      };
    });
    
    return Promise.resolve({
      code: 200,
      msg: "查询成功",
      data: audioList
    });
  }
  
  // 如果没有找到或电台没有音频，返回空数组
  return Promise.resolve({
    code: 200,
    msg: "查询成功",
    data: []
  });
} 