<template>
  <!-- 
    音频管理页面
    功能：
    1. 条件显示返回电台管理按钮（从电台进入时显示，从菜单进入时不显示）
    2. 根据不同入口来源，调整查询参数和显示逻辑
    3. 支持从不同入口添加、编辑、播放音频
    4. 提供电台选择功能（非电台进入时）
  -->
  <div class="app-container">
    <!-- 添加页面头部 -->
    <div class="page-header" v-if="isFromRadio">
      <el-button icon="Back" @click="goBack">返回电台管理</el-button>
      <div class="page-title">{{ radioName }}</div>
    </div>

    <!-- 固定显示播放器组件 -->
    <div class="global-player">
      <audio-player
        v-if="currentAudio.url"
        :audio-url="currentAudio.url"
        :audio-name="currentAudio.name"
        @ended="onAudioEnded"
        @play="onAudioPlay"
        @pause="onAudioPause"
      />
      <div v-else class="empty-player">
        <el-icon><Headset /></el-icon>
        <span>点击音频可以播放</span>
      </div>
    </div>

    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="音频编号" prop="id">
        <el-input
          v-model="queryParams.id"
          placeholder="请输入音频编号"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="音频名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入音频名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="音频状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择状态"
          clearable
          style="width: 200px"
        >
          <el-option label="全部" value="" />
          <el-option label="启用" value="1" />
          <el-option label="禁用" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" style="width: 300px">
        <el-date-picker
          v-model="dateRange"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['listen:audio:add']"
        >新增</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="audioList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column label="音频编号" align="center" prop="id" width="80" />
      <el-table-column label="音频名称" prop="name" :show-overflow-tooltip="true" min-width="150" />
      <el-table-column label="电台名称" prop="radioName" :show-overflow-tooltip="true" min-width="120" />
      <el-table-column label="文件大小" align="center" width="90">
        <template #default="scope">
          {{ formatFileSize(scope.row.file_size || 0) }}
        </template>
      </el-table-column>
      <el-table-column label="时长" align="center" width="70">
        <template #default="scope">
          {{ formatTime(scope.row.seconds || 0) }}
        </template>
      </el-table-column>
      <el-table-column label="播放次数" align="center" prop="play_count" width="80" />
      <el-table-column label="音频状态" align="center" width="80">
        <template #default="scope">
          <dict-tag :options="[
            { label: '启用', value: '1', elTagType: 'success' },
            { label: '禁用', value: '0', elTagType: 'danger' }
          ]" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="排序" align="center" prop="order" width="70" />
      <el-table-column label="创建时间" align="center" prop="created" width="150">
        <template #default="scope">
          <span>{{ parseTime(scope.row.created) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="试听" align="center" width="60">
        <template #default="scope">
          <el-tooltip :content="currentAudio.id === scope.row.id && isPlaying ? '暂停' : '播放'" placement="top">
            <el-button 
              circle 
              :icon="currentAudio.id === scope.row.id && isPlaying ? 'VideoPause' : 'VideoPlay'" 
              @click="playAudio(scope.row)"
              size="small"
            />
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="160" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-tooltip content="修改" placement="top">
            <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['listen:audio:edit']"></el-button>
          </el-tooltip>
          <el-tooltip :content="scope.row.status === '0' ? '启用' : '禁用'" placement="top">
            <el-button
              link
              type="primary"
              :icon="scope.row.status === '0' ? 'Check' : 'Close'"
              v-if="scope.row.status === '0' || scope.row.status === '1'"
              @click="handleStatusChange(scope.row, scope.row.status === '0' ? '1' : '0')"
              v-hasPermi="['listen:audio:edit']"
            ></el-button>
          </el-tooltip>
          <el-tooltip content="删除" placement="top">
            <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['listen:audio:remove']"></el-button>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改音频对话框 -->
    <el-dialog :title="title" v-model="open" width="700px" append-to-body>
      <el-form ref="audioFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="音频名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入音频名称" />
        </el-form-item>

        <!-- 电台选择，只在非电台进入时显示 -->
        <el-form-item v-if="!isFromRadio" label="所属电台" prop="radioId">
          <el-select v-model="form.radioId" placeholder="请选择所属电台" style="width: 100%">
            <el-option 
              v-for="radio in radioOptions" 
              :key="radio.id" 
              :label="radio.name" 
              :value="radio.id"
              @click="form.radioName = radio.name"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="上传音频" prop="audioFiles" class="upload-audio-item">
          <div class="upload-container">
            <div class="upload-header">
              <el-upload
                class="audio-uploader"
                action="#"
                :http-request="uploadAudioRequest"
                :show-file-list="false"
                :before-upload="beforeAudioUpload"
                accept="audio/mp3"
                multiple
                ref="uploadRef"
              >
                <el-button type="primary" plain size="default" icon="Upload">上传文件</el-button>
                <span class="upload-tip">支持批量上传，上传格式为mp3文件，单个文件大小不超过20MB</span>
              </el-upload>
            </div>

            <div class="audio-files-table" v-if="uploadFiles.length > 0">
              <div class="file-table-wrapper">
                <div class="file-table-header">
                  <div class="file-name-header">文件名</div>
                  <div class="file-size-header">大小</div>
                  <div class="file-status-header">上传状态</div>
                  <div class="file-operation-header">操作</div>
                  <div class="file-enabled-header">启用状态</div>
                </div>
                <div class="file-table-body">
                  <div v-for="(file, index) in uploadFiles" :key="file.uid" class="file-table-row">
                    <div class="file-name">{{ file.name }}</div>
                    <div class="file-size">{{ formatFileSize(file.size) }}</div>
                    <div class="file-status">
                      <span v-if="file.status === 'success'" class="status-success">
                        <el-icon><Check /></el-icon> 上传成功
                      </span>
                      <span v-else-if="file.status === 'error'" class="status-error">
                        <el-icon><Close /></el-icon> 上传失败
                        <el-tooltip effect="dark" placement="top">
                          <template #content>
                            <span>{{ file.error || '上传失败，请重试' }}</span>
                          </template>
                          <el-icon class="error-info-icon"><Warning /></el-icon>
                        </el-tooltip>
                      </span>
                      <div v-else-if="file.status === 'uploading'" class="upload-progress">
                        <el-progress :percentage="file.percentage" :stroke-width="5" :show-text="false" />
                        <span>上传{{ file.percentage }}%...</span>
                      </div>
                      <span v-else-if="file.status === 'waiting'" class="status-waiting">
                        <el-icon><Loading /></el-icon> 待上传
                      </span>
                    </div>
                    <div class="file-operation">
                      <span
                        class="status-link"
                        @click="removeFile(index)"
                        :class="{'disabled': file.status === 'uploading'}"
                      >删除</span>
                    </div>
                    <div class="file-enabled">
                      <el-switch
                        v-model="file.enabled"
                        :disabled="file.status !== 'success'"
                        @change="toggleFileStatus(index)"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Audio">
import { listAudio, getAudio, addAudio, updateAudio, enableAudio, disableAudio, delAudio } from "@/api/listen/audio";
import { listRadio } from "@/api/listen/radio"; // 导入获取电台列表API
import AudioPlayer from "@/components/AudioPlayer/index.vue";
import { onMounted, nextTick } from 'vue';
import { parseTime } from '@/utils/ruoyi';
import DictTag from '@/components/DictTag/index.vue';

const { proxy } = getCurrentInstance();
const router = useRouter();
const route = useRoute();

const audioList = ref([]);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const dateRange = ref([]);
const title = ref("");
const open = ref(false);
const radioId = ref(route.query.radioId);
const radioName = ref(route.query.radioName || '电台');
// 判断是否从电台页面进入
const isFromRadio = ref(!!route.query.radioId && !!route.query.radioName);

// 音频播放相关
const currentAudio = ref({
  id: null,
  name: '',
  url: '',
  duration: 0
});
const isPlaying = ref(false);

// 上传文件相关
const uploadRef = ref(null);
const uploadFiles = ref([]);
const uploadQueue = ref([]);
const isUploading = ref(false);

// 电台选项列表
const radioOptions = ref([]);

const data = reactive({
  form: {
    id: undefined,
    radioId: isFromRadio.value ? radioId.value : undefined,
    radioName: isFromRadio.value ? radioName.value : '',
    name: '',
    file_url: '',
    file_id: undefined,
    file_size: 0,
    audioFiles: [],
    seconds: 0,
    order: 0,
    play_count: 0,
    status: '1'
  },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    radioId: radioId.value,
    id: undefined,
    name: undefined,
    status: undefined
  },
  rules: {
    name: [
      { required: true, message: "音频名称不能为空", trigger: "blur" }
    ],
    radioId: [
      { required: !isFromRadio.value, message: "请选择所属电台", trigger: "change" }
    ],
    audioFiles: [
      { required: true, type: 'array', min: 1, message: "请上传至少一个音频文件", trigger: "change" }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询音频列表 */
function getList() {
  loading.value = true;
  // 构建查询参数
  const params = proxy.addDateRange(queryParams.value, dateRange.value);
  
  // 如果不是从电台管理页面进入，移除radioId查询条件
  if (!isFromRadio.value) {
    params.radioId = undefined;
  }
  
  listAudio(params).then(response => {
    audioList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 返回电台列表 */
function goBack() {
  router.push('/listen/radio');
}

/** 格式化时间 */
function formatTime(seconds) {
  if (!seconds) return '00:00';
  seconds = Math.floor(seconds);
  const minutes = Math.floor(seconds / 60);
  seconds = seconds % 60;
  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
}

/** 格式化文件大小 */
function formatFileSize(bytes) {
  if (!bytes) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/** 移除文件 */
function removeFile(index) {
  // 如果文件正在上传中，不允许删除
  if (uploadFiles.value[index].status === 'uploading') {
    return;
  }

  uploadFiles.value.splice(index, 1);
  form.value.audioFiles = uploadFiles.value.filter(file => file.status === 'success');
}

/** 切换文件状态 */
function toggleFileStatus(index) {
  const file = uploadFiles.value[index];
  file.enabled = !file.enabled;

  // 更新表单中的音频文件列表
  form.value.audioFiles = uploadFiles.value.filter(file => file.status === 'success');
}

/** 播放音频 */
function playAudio(audio) {
  if (currentAudio.value.id === audio.id) {
    // 如果是当前正在播放的音频，则切换播放状态
    isPlaying.value = !isPlaying.value;
    return;
  }

  currentAudio.value = {
    id: audio.id,
    name: audio.name,
    url: audio.file_url,
    duration: audio.seconds || 0
  };
  
  isPlaying.value = true;
  
  // 更新播放次数（实际项目中应该通过API更新）
  audio.play_count = (audio.play_count || 0) + 1;
}

/** 音频播放事件 */
function onAudioPlay() {
  isPlaying.value = true;
}

/** 音频暂停事件 */
function onAudioPause() {
  isPlaying.value = false;
}

/** 音频播放结束事件 */
function onAudioEnded() {
  isPlaying.value = false;
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = [];
  proxy.resetForm("queryRef");
  
  // 如果是从电台页面进入，重置后保留radioId查询条件
  if (isFromRadio.value) {
    queryParams.value.radioId = radioId.value;
  }
  
  handleQuery();
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  form.value = {
    id: undefined,
    radioId: isFromRadio.value ? radioId.value : undefined,
    radioName: isFromRadio.value ? radioName.value : '',
    name: '',
    file_url: '',
    file_id: undefined,
    file_size: 0,
    audioFiles: [],
    seconds: 0,
    order: 0,
    play_count: 0,
    status: '1'
  };

  // 清空上传文件列表
  uploadFiles.value = [];
  uploadQueue.value = [];
  isUploading.value = false;

  proxy.resetForm("audioFormRef");
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加音频";
  
  // 如果从电台页面进入，设置电台相关信息
  if (isFromRadio.value) {
    form.value.radioId = radioId.value;
    form.value.radioName = radioName.value;
  } else {
    // 否则需要用户选择电台
    form.value.radioId = undefined;
    form.value.radioName = '';
    // 这里可以添加电台选择逻辑，但目前代码中没有实现
  }
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const audioId = row.id || ids.value;
  getAudio(audioId).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改音频";
  });
}

/** 状态修改 */
function handleStatusChange(row, status) {
  const text = status === "1" ? "启用" : "禁用";
  proxy.$modal.confirm('确认要"' + text + '""' + row.name + '"音频吗?').then(function() {
    const audioId = row.id;
    // 调用状态修改API
    if (status === "1") {
      // 启用
      return enableAudio(audioId);
    } else {
      // 禁用
      return disableAudio(audioId);
    }
  }).then(() => {
    proxy.$modal.msgSuccess(text + "成功");
    getList();
  }).catch(() => {});
}

/** 删除按钮操作 */
function handleDelete(row) {
  const audioIds = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除音频编号为"' + audioIds + '"的数据项?').then(function() {
    return delAudio(audioIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["audioFormRef"].validate(valid => {
    if (valid) {
      // 检查是否有正在上传的文件
      if (isUploading.value) {
        proxy.$modal.msgWarning("请等待所有文件上传完成");
        return;
      }

      // 检查是否有上传成功的文件
      const successFiles = uploadFiles.value.filter(file => file.status === 'success');
      if (successFiles.length === 0) {
        proxy.$modal.msgWarning("请上传至少一个音频文件");
        return;
      }
      
      // 检查是否选择了电台
      if (!form.value.radioId && !isFromRadio.value) {
        proxy.$modal.msgWarning("请选择电台");
        return;
      }

      // 准备提交的数据
      const submitData = {
        ...form.value,
        // 只提交启用状态的音频文件
        audioFiles: successFiles.filter(file => file.enabled).map(file => ({
          name: file.name,
          url: file.url,
          seconds: file.duration,
          size: file.size
        }))
      };

      if (submitData.id != undefined) {
        updateAudio(submitData).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addAudio(submitData).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 音频上传前的校验 */
function beforeAudioUpload(file) {
  const isMp3 = file.type === 'audio/mp3' || file.name.toLowerCase().endsWith('.mp3');
  const isLt20M = file.size / 1024 / 1024 < 20;

  if (!isMp3) {
    proxy.$modal.msgError('只能上传MP3格式的音频文件!');
    return false;
  }
  if (!isLt20M) {
    proxy.$modal.msgError('音频文件大小不能超过 20MB!');
    return false;
  }

  // 添加到上传文件列表
  const fileObj = {
    uid: Date.now() + uploadFiles.value.length,
    name: file.name,
    size: file.size,
    file: file,
    status: 'waiting',
    percentage: 0,
    url: '',
    duration: 0,
    enabled: true,
    error: ''
  };

  uploadFiles.value.push(fileObj);
  uploadQueue.value.push(fileObj);

  // 如果当前没有上传任务，开始上传
  if (!isUploading.value) {
    processUploadQueue();
  }

  return false; // 阻止默认上传行为，我们使用自定义上传
}

/** 处理上传队列 */
function processUploadQueue() {
  if (uploadQueue.value.length === 0) {
    isUploading.value = false;
    return;
  }

  isUploading.value = true;
  const fileObj = uploadQueue.value[0];
  const index = uploadFiles.value.findIndex(item => item.uid === fileObj.uid);

  if (index === -1) {
    // 文件已被移除
    uploadQueue.value.shift();
    processUploadQueue();
    return;
  }

  // 更新状态为上传中
  uploadFiles.value[index].status = 'uploading';

  // 创建FormData
  const formData = new FormData();
  formData.append('file', fileObj.file);
  // 使用表单中的radioId，确保在非电台进入时能正确关联
  formData.append('radioId', form.value.radioId || '');

  // Mock上传过程
  let progress = 0;
  const interval = setInterval(() => {
    progress += Math.floor(Math.random() * 10) + 1;
    if (progress >= 100) {
      clearInterval(interval);
      progress = 100;
      
      // 模拟上传完成后的处理
      setTimeout(() => {
        // 随机成功或失败（实际项目中应该是真实的上传结果）
        const isSuccess = Math.random() > 0.2; // 80%概率成功
        
        if (isSuccess) {
          // 上传成功
          uploadFiles.value[index].status = 'success';
          uploadFiles.value[index].url = `https://www.soundhelix.com/examples/mp3/SoundHelix-Song-${Math.floor(Math.random() * 10) + 1}.mp3`;
          uploadFiles.value[index].duration = Math.floor(Math.random() * 180) + 60; // 60-240秒
          
          // 更新表单中的音频文件列表
          form.value.audioFiles = uploadFiles.value.filter(file => file.status === 'success' && file.enabled);
          
          // 如果音频名称为空，使用第一个成功上传的文件名作为音频名称
          if (!form.value.name && uploadFiles.value.filter(file => file.status === 'success').length === 1) {
            form.value.name = fileObj.name.split('.')[0];
            
            // 更新音频相关信息到表单
            form.value.file_url = uploadFiles.value[index].url;
            form.value.seconds = uploadFiles.value[index].duration;
            form.value.file_size = uploadFiles.value[index].size;
          }
        } else {
          // 上传失败
          uploadFiles.value[index].status = 'error';
          uploadFiles.value[index].error = '服务器错误，请重试';
        }
        
        // 移除队列中的第一个元素
        uploadQueue.value.shift();
        
        // 继续处理队列
        processUploadQueue();
      }, 500);
    } else {
      uploadFiles.value[index].percentage = progress;
    }
  }, 200);
}

/** 自定义音频上传（兼容旧接口） */
function uploadAudioRequest() {
  // 这个方法不再实际使用，但保留以兼容el-upload组件
  return { status: 'success' };
}

/** 选择条数  */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 初始化播放器 */
function initPlayer() {
  currentAudio.value = {
    id: null,
    name: '',
    url: '',
    duration: 0
  };
  isPlaying.value = false;
}

/** 获取电台列表 */
function getRadioOptions() {
  if (!isFromRadio.value) {
    // 只有在非电台进入时才需要获取电台列表
    listRadio({pageSize: 100}).then(response => {
      radioOptions.value = response.rows || [];
    });
  }
}

// 组件挂载时执行
onMounted(() => {
  initPlayer();
  // 确保页面加载时立即获取数据
  nextTick(() => {
    getList();
    getRadioOptions(); // 获取电台选项
  });
  
  // 根据来源调整查询参数
  if (!isFromRadio.value) {
    // 如果不是从电台页面进入，则清空radioId查询条件
    queryParams.value.radioId = undefined;
  }
});
</script>

<style scoped>
.mb8 {
  margin-bottom: 8px;
}

.small-padding {
  padding-left: 5px;
  padding-right: 5px;
}

.fixed-width {
  min-width: 120px;
}

.page-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  gap: 16px;
}

.page-title {
  font-size: 18px;
  font-weight: bold;
  color: var(--el-text-color-primary);
}

.upload-container {
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
  padding: 20px;
  background-color: #fff;
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.05);
  position: relative;
}

.upload-container:hover {
  border-color: #409EFF;
}

.upload-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.audio-uploader {
  display: flex;
  align-items: center;
}

.upload-tip {
  margin-left: 10px;
  color: #909399;
  font-size: 13px;
}

.audio-files-table {
  margin-top: 15px;
  width: 100%;
}

.file-table-wrapper {
  width: 100%;
  border-collapse: collapse;
  overflow: hidden;
}

.file-table-header {
  display: flex;
  background-color: #fff;
  padding: 12px 0;
  font-weight: normal;
  color: #909399;
  border: 1px solid #dfe6ec;
  border-bottom: none;
  box-sizing: border-box;
}

.file-table-body {
  max-height: 300px;
  overflow-y: auto;
  background-color: #fff;
}

.file-table-row {
  display: flex;
  padding: 12px 0;
  border: 1px solid #dfe6ec;
  transition: background-color 0.3s;
}

.file-table-row:hover {
  background-color: #f5f7fa;
}

.file-table-row + .file-table-row {
  border-top: none;
}

.file-name-header,
.file-name {
  flex: 3;
  padding: 0 15px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  border-right: 1px solid #dfe6ec;
  display: flex;
  align-items: center;
}

.file-name-header {
  color: #606266;
  font-size: 14px;
}

.file-size-header,
.file-size {
  flex: 1;
  padding: 0 10px;
  text-align: center;
  color: #606266;
  border-right: 1px solid #dfe6ec;
  display: flex;
  align-items: center;
  justify-content: center;
}

.file-size-header {
  color: #606266;
  font-size: 14px;
}

.file-status-header,
.file-status {
  flex: 2;
  padding: 0 10px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  border-right: 1px solid #dfe6ec;
}

.file-status-header {
  color: #606266;
  font-size: 14px;
}

.file-operation-header,
.file-operation {
  flex: 1;
  padding: 0 10px;
  text-align: center;
  border-right: 1px solid #dfe6ec;
  display: flex;
  align-items: center;
  justify-content: center;
}

.file-operation-header {
  color: #606266;
  font-size: 14px;
}

.file-enabled-header,
.file-enabled {
  flex: 1;
  padding: 0 10px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

.file-enabled-header {
  color: #606266;
  font-size: 14px;
}

.status-success {
  color: #67c23a;
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-success i {
  margin-right: 5px;
  font-size: 16px;
}

.status-error {
  color: #f56c6c;
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-error i {
  margin-right: 5px;
  font-size: 16px;
}

.status-waiting {
  color: #909399;
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-waiting i {
  margin-right: 5px;
  font-size: 16px;
  animation: rotating 2s linear infinite;
}

.upload-progress {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

.upload-progress .el-progress {
  width: 100%;
}

.upload-progress span {
  font-size: 12px;
  color: #409EFF;
  margin-top: 5px;
}

@keyframes rotating {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.status-link {
  color: #409EFF;
  cursor: pointer;
  font-size: 14px;
}

.status-link:hover {
  color: #66b1ff;
  text-decoration: underline;
}

.status-link.disabled {
  color: #c0c4cc;
  cursor: not-allowed;
  text-decoration: none;
}

.status-link.disabled:hover {
  color: #c0c4cc;
  text-decoration: none;
}

.ml-10 {
  margin-left: 10px;
}

/* 全局播放器样式 */
.global-player {
  position: sticky;
  top: 0;
  z-index: 999;
  margin-bottom: 20px;
  background: var(--el-bg-color);
  border-radius: 4px;
  padding: 10px;
  box-shadow: var(--el-box-shadow-light);
  min-height: 80px;
}

.empty-player {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60px;
  color: var(--el-text-color-secondary);
  font-size: 14px;
}

.empty-player .el-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.error-info-icon {
  margin-left: 5px;
  color: #f56c6c;
  cursor: pointer;
}
</style>
