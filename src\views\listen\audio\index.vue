<template>
  <div class="app-container">
    <!-- 电台选择和标题区域 -->
    <div class="radio-selector-area">
      <div class="radio-title" v-if="selectedRadioName">
        <h2>{{ selectedRadioName }} - 音频列表</h2>
      </div>
      <div class="radio-selector" v-else>
        <h2>未选择音频所属电台</h2>
      </div>
      <el-select 
        v-model="selectedRadioId" 
        placeholder="请选择所属电台" 
        style="width: 300px"
        filterable
        clearable
        @change="handleRadioChange"
        :loading="radioLoading"
      >
        <el-option 
          v-for="radio in radioOptions" 
          :key="radio.id" 
          :label="radio.name" 
          :value="radio.id"
        />
      </el-select>
    </div>

    <div class="query-and-player-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" class="query-form">
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 音频播放器组件，只在选择了电台时显示 -->
      <div class="player-wrapper" v-if="selectedRadioId">
        <audio-player
          :audio-url="playList.length > 0 ? playList : ['']"
          :audio-name="playListNames.length > 0 ? playListNames : ['']"
          :initial-index="currentPlayIndex"
          :autoplay="playList.length > 0"
          :single-mode="true"
          @ended="onAudioEnded"
          @play="onAudioPlay"
          @pause="onAudioPause"
          @track-change="onTrackChange"
          ref="audioPlayerRef"
        />
      </div>
    </div>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5" v-if="selectedRadioId">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['system:audio:add']"
        >新增</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-empty v-if="!selectedRadioId" description="请先选择电台"></el-empty>

    <el-table v-else v-loading="loading" :data="audioList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="音频编号" align="center" prop="id" width="180" :show-overflow-tooltip="true" />
      <el-table-column label="音频名称" align="center" prop="name" :show-overflow-tooltip="true">
        <template #default="scope">
          <div class="audio-name-cell">
            <el-tooltip :content="scope.row.name" placement="top" :show-after="500">
              <span>{{ scope.row.name }}</span>
            </el-tooltip>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="时长" align="center" width="100" :show-overflow-tooltip="true">
        <template #default="scope">
          <span class="duration-tag">{{ formatTime(scope.row.seconds || 0) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="音频播放排序" align="center" prop="sort" width="120" :show-overflow-tooltip="true" />
      <el-table-column label="音频状态" align="center" width="80" :show-overflow-tooltip="true">
        <template #default="scope">
          <dict-tag :options="[
            { label: '启用', value: 1, elTagType: 'success' },
            { label: '禁用', value: 0, elTagType: 'danger' }
          ]" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="试听" align="center" width="100" :show-overflow-tooltip="true">
        <template #default="scope">
          <el-tooltip :content="scope.row.status === 0 ? '音频已禁用，无法试听' : (currentAudio.id === scope.row.id && isPlaying ? '暂停' : '播放')" placement="top">
            <div class="play-button-container">
              <el-button 
                circle 
                :icon="currentAudio.id === scope.row.id && isPlaying ? 'VideoPause' : 'VideoPlay'" 
                @click="playAudio(scope.row)"
                size="small"
                :disabled="scope.row.status === 0"
                :type="scope.row.status === 0 ? 'info' : (currentAudio.id === scope.row.id && isPlaying ? 'danger' : 'primary')"
                class="play-button"
                :class="{'playing': currentAudio.id === scope.row.id && isPlaying, 'disabled': scope.row.status === 0}"
              />
            </div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="created" width="180" :show-overflow-tooltip="true">
        <template #default="scope">
          <span>{{ parseTime(scope.row.created) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="更新时间" align="center" prop="updated" width="180" :show-overflow-tooltip="true">
        <template #default="scope">
          <span>{{ parseTime(scope.row.updated) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="180" class-name="small-padding fixed-width" :show-overflow-tooltip="true">
        <template #default="scope">
          <div class="operation-buttons">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:audio:edit']"></el-button>
            </el-tooltip>
            <el-tooltip :content="scope.row.status === 0 ? '启用' : '禁用'" placement="top">
              <el-button
                link
                type="primary"
                :icon="scope.row.status === 0 ? 'Check' : 'Close'"
                v-if="scope.row.status === 0 || scope.row.status === 1"
                @click="handleStatusChange(scope.row, scope.row.status === 0 ? 1 : 0)"
                v-hasPermi="['system:audio:edit']"
              ></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:audio:remove']"></el-button>
            </el-tooltip>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0 && selectedRadioId"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改音频对话框 -->
    <el-dialog :title="title" v-model="open" width="700px" append-to-body>
      <el-form ref="audioFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="音频名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入音频名称" />
        </el-form-item>

        <el-form-item label="上传音频" prop="audioFiles" class="upload-audio-item">
          <div class="upload-container">
            <div class="upload-header">
              <el-upload
                class="audio-uploader"
                action="#"
                :http-request="uploadAudioRequest"
                :show-file-list="false"
                :before-upload="beforeAudioUpload"
                accept="audio/mp3"
                multiple
                ref="uploadRef"
              >
                <el-button type="primary" plain size="default" icon="Upload">上传文件</el-button>
                <span class="upload-tip">支持批量上传，上传格式为mp3文件，单个文件大小不超过20MB</span>
              </el-upload>
            </div>

            <div class="audio-files-table" v-if="uploadFiles.length > 0">
              <div class="file-table-wrapper">
                <div class="file-table-header">
                  <div class="file-name-header">文件名</div>
                  <div class="file-size-header">大小</div>
                  <div class="file-status-header">上传状态</div>
                  <div class="file-operation-header">操作</div>
                </div>
                <div class="file-table-body">
                  <div v-for="(file, index) in uploadFiles" :key="file.uid" class="file-table-row">
                    <div class="file-name">{{ file.name }}</div>
                    <div class="file-size">{{ formatFileSize(file.size) }}</div>
                    <div class="file-status">
                      <span v-if="file.status === 'success'" class="status-success">
                        <el-icon><Check /></el-icon> 上传成功
                      </span>
                      <span v-else-if="file.status === 'error'" class="status-error">
                        <el-icon><Close /></el-icon> 上传失败
                        <el-tooltip effect="dark" placement="top">
                          <template #content>
                            <span>{{ file.error || '上传失败，请重试' }}</span>
                          </template>
                          <el-icon class="error-info-icon"><Warning /></el-icon>
                        </el-tooltip>
                      </span>
                      <div v-else-if="file.status === 'uploading'" class="upload-progress">
                        <el-progress :percentage="file.percentage" :stroke-width="5" :show-text="false" />
                        <span>上传{{ file.percentage }}%...</span>
                      </div>
                      <span v-else-if="file.status === 'waiting'" class="status-waiting">
                        <el-icon><Loading /></el-icon> 待上传
                      </span>
                    </div>
                    <div class="file-operation">
                      <span
                        class="status-link"
                        @click="removeFile(index)"
                        :class="{'disabled': file.status !== 'success' && file.status !== 'error'}"
                        v-if="file.status === 'success' || file.status === 'error'"
                      >删除</span>
                      <span v-else class="status-link disabled">删除</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-form-item>
        
        <!-- 新增音频状态字段 -->
        <el-form-item label="音频状态" prop="status">
          <el-select v-model="form.status" placeholder="请选择音频状态" style="width: 100%">
            <el-option label="启用" :value="'1'" />
            <el-option label="禁用" :value="'0'" />
          </el-select>
        </el-form-item>

        <el-form-item label="所属电台" prop="radioId">
          <el-select v-model="form.radioId" placeholder="请选择所属电台">
            <el-option
              v-for="item in radioOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="audio">
import { listAudio, getAudio, addAudio, updateAudio, changeAudioStatus, delAudio, uploadAudioFile, delAudioFile } from "@/api/listen/audio";
import { listAllRadio } from "@/api/listen/radio";
import AudioPlayer from "@/components/AudioPlayer/index.vue";
import { onMounted, nextTick, watch } from 'vue';
import { parseTime } from '@/utils/ruoyi';
import { addQueryDateRange } from '@/utils/dateUtils';
import DictTag from '@/components/DictTag/index.vue';

const { proxy } = getCurrentInstance();
const router = useRouter();
const route = useRoute();

const audioList = ref([]);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const dateRange = ref([]);
const title = ref("");
const open = ref(false);
const radioId = ref(undefined);
const radioName = ref('');
const isFromRadio = ref(false);

// 新增：选择的电台ID和名称
const selectedRadioId = ref(null);
const selectedRadioName = ref('');

// 音频播放相关
const currentAudio = ref({
  id: null,
  name: '',
  url: '',
  audioUrl: '',
  fileUrl: '',
  duration: 0
});
const isPlaying = ref(false);

// 新增播放列表相关数据
const playList = ref([]);
const playListNames = ref([]);
const currentPlayIndex = ref(0);

// 上传文件相关
const uploadRef = ref(null);
const uploadFiles = ref([]);
const uploadQueue = ref([]);
const isUploading = ref(false);

// 电台选项列表
const radioOptions = ref([]);
const radioLoading = ref(false);

// 引用播放器组件
const audioPlayerRef = ref(null);

const data = reactive({
  form: {
    id: undefined,
    radioId: selectedRadioId.value,
    radioName: selectedRadioName.value,
    name: '',
    fileUrl: '',
    audioUrl: '',
    fileId: undefined,
    fileSize: 0,
    audioFiles: [],
    seconds: 0,
    sort: 0,
    status: '0'
  },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    radioId: undefined,
    id: undefined,
    name: undefined,
    status: undefined,
    beginTime: undefined,
    endTime: undefined
  },
  rules: {
    name: [
      { required: true, message: "音频名称不能为空", trigger: "blur" }
    ],
    audioFiles: [
      { required: true, type: 'array', min: 1, message: "请上传至少一个音频文件", trigger: "change" }
    ],
    status: [
      { required: true, message: "请选择音频状态", trigger: "change" }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 重置按钮操作 */
function resetQuery() {
  // 不再需要重置dateRange，因为日期选择器已移除
  // dateRange.value = [];
  
  proxy.resetForm("queryRef");
  
  // 重置电台选择
  selectedRadioId.value = null;
  selectedRadioName.value = '';
  queryParams.value.radioId = undefined;
  
  // 重置播放器状态
  resetPlayer();
  
  // 清空列表数据
  audioList.value = [];
  total.value = 0;
}

/** 电台变更处理 */
function handleRadioChange(val) {
  // 先重置播放器状态
  resetPlayer();
  
  if (val) {
    const radio = radioOptions.value.find(item => item.id === val);
    if (radio) {
      selectedRadioName.value = radio.name;
      queryParams.value.radioId = val;
      getList();
    }
  } else {
    selectedRadioName.value = '';
    queryParams.value.radioId = undefined;
    // 清空列表数据
    audioList.value = [];
    total.value = 0;
  }
}

/** 查询音频列表 */
function getList() {
  // 如果没有选择电台，则不查询数据
  if (!selectedRadioId.value) {
    loading.value = false;
    return;
  }
  
  loading.value = true;
  
  // 创建查询参数的副本
  const params = { ...queryParams.value };
  // 确保使用选中的电台ID
  params.radioId = selectedRadioId.value;
  
  // 使用新的日期工具函数添加日期范围参数
  const finalParams = addQueryDateRange(params, dateRange.value);
  
  // 使用处理后的参数查询列表
  listAudio(finalParams).then(response => {
    audioList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  }).catch(error => {
    console.error("获取音频列表失败:", error);
    loading.value = false;
  });
}

/** 重置播放器 */
function resetPlayer() {
  // 如果正在播放，先暂停
  if (isPlaying.value && audioPlayerRef.value) {
    audioPlayerRef.value.pause();
  }
  
  // 重置所有播放器相关状态
  currentAudio.value = {
    id: null,
    name: '',
    url: '',
    audioUrl: '',
    fileUrl: '',
    duration: 0
  };
  isPlaying.value = false;
  playList.value = [];
  playListNames.value = [];
  currentPlayIndex.value = 0;
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  form.value = {
    id: undefined,
    radioId: selectedRadioId.value,
    radioName: selectedRadioName.value,
    name: '',
    fileUrl: '',
    audioUrl: '',
    fileId: undefined,
    fileSize: 0,
    audioFiles: [],
    seconds: 0,
    sort: 0,
    status: '0'
  };

  // 清空上传文件列表
  uploadFiles.value = [];
  uploadQueue.value = [];
  isUploading.value = false;

  proxy.resetForm("audioFormRef");
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  // 确保使用当前选中的电台ID和名称
  form.value.radioId = selectedRadioId.value;
  form.value.radioName = selectedRadioName.value;
  open.value = true;
  title.value = "添加音频";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const audioId = row.id || ids.value;
  getAudio(audioId).then(response => {
    form.value = response.data;
    // 确保将排序字段正确映射
    if (response.data.sort !== undefined) {
      form.value.sort = response.data.sort;
    } else if (response.data.order !== undefined) {
      // 兼容旧数据，如果有order字段则映射到sort
      form.value.sort = response.data.order;
    }
    
    // 确保使用当前选择的电台
    form.value.radioId = selectedRadioId.value;
    form.value.radioName = selectedRadioName.value;
    
    // 确保音频状态为字符串类型
    if (form.value.status !== undefined) {
      form.value.status = String(form.value.status);
    }
    
    open.value = true;
    title.value = "修改音频";
  });
}

/** 状态修改 */
function handleStatusChange(row, status) {
  const text = status === 1 ? "启用" : "禁用";
  proxy.$modal.confirm('确认要"' + text + '""' + row.name + '"音频吗?').then(function() {
    return changeAudioStatus(row.id, status);
  }).then(() => {
    proxy.$modal.msgSuccess(text + "成功");
    
    // 如果修改的是当前正在播放的音频的状态
    if (row.id === currentAudio.value.id) {
      // 如果设为禁用，停止播放并清空播放列表
      if (status === 0) {
        isPlaying.value = false;
        playList.value = [];
        playListNames.value = [];
        currentAudio.value = {
          id: null,
          name: '',
          url: '',
          audioUrl: '',
          fileUrl: '',
          duration: 0
        };
      }
    }
    
    // 刷新列表
    getList();
    
    // 如果还有正在播放的音频，重新初始化播放列表
    if (currentAudio.value.id) {
      // 延迟执行，确保audioList已更新
      setTimeout(() => {
        const audioToPlay = audioList.value.find(item => item.id === currentAudio.value.id);
        if (audioToPlay && audioToPlay.status === 1) {
          initSinglePlayList(audioToPlay);
        }
      }, 300);
    }
  }).catch(() => {});
}

/** 删除按钮操作 */
function handleDelete(row) {
  const audioIds = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除音频编号为"' + audioIds + '"的数据项?').then(function() {
    return delAudio(audioIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["audioFormRef"].validate(valid => {
    if (valid) {
      // 检查是否有正在上传的文件
      if (isUploading.value) {
        proxy.$modal.msgWarning("请等待所有文件上传完成");
        return;
      }

      // 检查是否有上传成功的文件
      const successFiles = uploadFiles.value.filter(file => file.status === 'success');
      if (successFiles.length === 0) {
        proxy.$modal.msgWarning("请上传至少一个音频文件");
        return;
      }
      
      // 确保使用当前选择的电台
      if (!selectedRadioId.value) {
        proxy.$modal.msgWarning("请先选择电台");
        return;
      }

      // 准备提交的数据
      const submitData = {
        ...form.value,
        // 确保使用当前选择的电台
        radioId: selectedRadioId.value,
        radioName: selectedRadioName.value,
        // 提交所有上传成功的音频文件
        audioFiles: successFiles.map(file => ({
          name: file.name,
          url: file.url,
          audioUrl: file.url,
          fileId: file.fileId,
          seconds: file.duration,
          fileSize: file.size
        }))
      };

      // 确保数值类型正确
      if (submitData.status !== undefined) {
        // 将字符串类型的状态值转换为数字类型，后端API需要数字类型
        submitData.status = Number(submitData.status);
      }
      
      if (submitData.sort !== undefined) {
        submitData.sort = Number(submitData.sort);
      }

      if (submitData.id != undefined) {
        updateAudio(submitData).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addAudio(submitData).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 音频上传前的校验 */
function beforeAudioUpload(file) {
  const isMp3 = file.type === 'audio/mp3' || file.name.toLowerCase().endsWith('.mp3');
  const isLt20M = file.size / 1024 / 1024 < 20;

  if (!isMp3) {
    proxy.$modal.msgError('只能上传MP3格式的音频文件!');
    return false;
  }
  if (!isLt20M) {
    proxy.$modal.msgError('音频文件大小不能超过 20MB!');
    return false;
  }

  // 检查文件名是否重复
  const isDuplicate = uploadFiles.value.some(item => item.name === file.name);
  if (isDuplicate) {
    proxy.$modal.msgError(`文件"${file.name}"已存在，请勿重复上传!`);
    return false;
  }

  // 添加到上传文件列表
  const fileObj = {
    uid: Date.now() + uploadFiles.value.length,
    name: file.name,
    size: file.size,
    file: file,
    status: 'waiting',
    percentage: 0,
    url: '',
    audioUrl: '',
    fileId: '',
    duration: 0,
    error: ''
  };

  uploadFiles.value.push(fileObj);
  uploadQueue.value.push(fileObj);

  // 如果当前没有上传任务，开始上传
  if (!isUploading.value) {
    processUploadQueue();
  }

  return false; // 阻止默认上传行为，我们使用自定义上传
}

/** 处理上传队列 */
function processUploadQueue() {
  if (uploadQueue.value.length === 0) {
    isUploading.value = false;
    return;
  }

  isUploading.value = true;
  const fileObj = uploadQueue.value[0];
  const index = uploadFiles.value.findIndex(item => item.uid === fileObj.uid);

  if (index === -1) {
    // 文件已被移除
    uploadQueue.value.shift();
    processUploadQueue();
    return;
  }

  // 更新状态为上传中
  uploadFiles.value[index].status = 'uploading';

  // 使用实际的上传API
  uploadAudioFile(fileObj.file, (progressEvent) => {
    if (progressEvent.total > 0) {
      uploadFiles.value[index].percentage = Math.round((progressEvent.loaded * 100) / progressEvent.total);
    }
  }).then(response => {
    // 上传成功
    if (response.code === 200) {
      // 单文件上传
      if (!response.data.successList && !response.data.failList) {
        uploadFiles.value[index].status = 'success';
        uploadFiles.value[index].url = response.data.url;
        uploadFiles.value[index].audioUrl = response.data.url;
        uploadFiles.value[index].fileId = response.data.fileId;
        uploadFiles.value[index].duration = response.data.duration || 0;
        
        // 更新表单中的音频文件列表
        form.value.audioFiles = uploadFiles.value.filter(file => file.status === 'success');
        
        // 如果音频名称为空，使用第一个成功上传的文件名作为音频名称
        if (!form.value.name && uploadFiles.value.filter(file => file.status === 'success').length === 1) {
          form.value.name = fileObj.name.split('.')[0];
          
          // 更新音频相关信息到表单
          form.value.fileUrl = uploadFiles.value[index].url;
          form.value.audioUrl = uploadFiles.value[index].url;
          form.value.fileId = uploadFiles.value[index].fileId;
          form.value.seconds = uploadFiles.value[index].duration;
          form.value.fileSize = uploadFiles.value[index].size;
        }
      } 
      // 多文件上传
      else if (response.data.successList && response.data.successList.length > 0) {
        const successFile = response.data.successList.find(item => item.fileName === fileObj.name);
        if (successFile) {
          uploadFiles.value[index].status = 'success';
          uploadFiles.value[index].url = successFile.url;
          uploadFiles.value[index].audioUrl = successFile.url;
          uploadFiles.value[index].fileId = successFile.fileId;
          uploadFiles.value[index].duration = successFile.duration || 0;
          
          // 更新表单中的音频文件列表
          form.value.audioFiles = uploadFiles.value.filter(file => file.status === 'success');
          
          // 如果音频名称为空，使用第一个成功上传的文件名作为音频名称
          if (!form.value.name && uploadFiles.value.filter(file => file.status === 'success').length === 1) {
            form.value.name = fileObj.name.split('.')[0];
            
            // 更新音频相关信息到表单
            form.value.fileUrl = uploadFiles.value[index].url;
            form.value.audioUrl = uploadFiles.value[index].url;
            form.value.fileId = uploadFiles.value[index].fileId;
            form.value.seconds = uploadFiles.value[index].duration;
            form.value.fileSize = uploadFiles.value[index].size;
          }
        } else {
          uploadFiles.value[index].status = 'error';
          uploadFiles.value[index].error = '上传失败';
        }
      } else {
        uploadFiles.value[index].status = 'error';
        uploadFiles.value[index].error = '上传失败';
      }
    } else {
      uploadFiles.value[index].status = 'error';
      uploadFiles.value[index].error = response.msg || '上传失败';
    }
  }).catch(error => {
    uploadFiles.value[index].status = 'error';
    uploadFiles.value[index].error = error.message || '上传失败';
  }).finally(() => {
    // 移除队列中的第一个元素
    uploadQueue.value.shift();
    
    // 继续处理队列
    processUploadQueue();
  });
}

/** 移除文件 */
function removeFile(index) {
  // 如果文件正在上传中，不允许删除
  if (uploadFiles.value[index].status === 'uploading') {
    return;
  }
  
  const file = uploadFiles.value[index];
  
  // 如果文件已上传成功且有fileId，则调用后端删除API
  if (file.status === 'success' && file.fileId) {
    delAudioFile(file.fileId).then(() => {
      uploadFiles.value.splice(index, 1);
      form.value.audioFiles = uploadFiles.value.filter(file => file.status === 'success');
      proxy.$modal.msgSuccess("文件删除成功");
    }).catch(() => {
      proxy.$modal.msgError("文件删除失败");
    });
  } else {
    // 文件未上传成功或上传失败，直接从列表中移除
    uploadFiles.value.splice(index, 1);
    form.value.audioFiles = uploadFiles.value.filter(file => file.status === 'success');
  }
}

/** 自定义音频上传（兼容旧接口） */
function uploadAudioRequest(options) {
  // 文件上传处理
  uploadAudioFile(options.file, (progressEvent) => {
    const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
    options.onProgress({ percent: percentCompleted });
  }).then(response => {
    options.onSuccess(response);
  }).catch(error => {
    options.onError(error);
  });
}

/** 选择条数  */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 初始化播放器 */
function initPlayer() {
  resetPlayer();
}

/** 获取电台列表 */
function getRadioOptions() {
  listAllRadio().then(response => {
    radioOptions.value = response.data || [];
  });
}

/** 远程搜索电台 */
function remoteSearchRadio(query) {
  if (query === '') {
    // 如果搜索词为空，调用原始获取电台列表方法
    getRadioOptions();
    return;
  }
  
  radioLoading.value = true;
  // 添加status参数，只获取启用状态(1)的电台
  listAllRadio({pageSize: 10, name: query, status: 1}).then(response => {
    radioOptions.value = response.rows || [];
    radioLoading.value = false;
  }).catch(error => {
    console.error("远程搜索电台失败:", error);
    radioLoading.value = false;
  });
}

/** 播放音频 */
function playAudio(audio) {
  // 如果音频被禁用，不允许播放
  if (audio.status === 0) {
    return;
  }
  
  // 如果当前点击的是正在播放的音频，则暂停播放
  if (currentAudio.value.id === audio.id && isPlaying.value) {
    // 如果播放器组件存在，调用其暂停方法
    if (audioPlayerRef.value) {
      audioPlayerRef.value.pause();
    }
    isPlaying.value = false;
    return;
  }
  
  // 设置当前音频信息
  currentAudio.value = {
    id: audio.id,
    name: audio.name,
    // 统一使用audioUrl或fileUrl
    url: audio.audioUrl || audio.fileUrl || '',
    audioUrl: audio.audioUrl || '',
    fileUrl: audio.fileUrl || '',
    duration: audio.seconds || 0
  };
  
  // 初始化播放列表 - 这里我们只放入当前音频
  initSinglePlayList(audio);
  
  // 设置为播放状态
  isPlaying.value = true;
}

/** 初始化单曲播放列表 */
function initSinglePlayList(audio) {
  // 清空当前播放列表
  playList.value = [];
  playListNames.value = [];
  
  // 只将当前音频添加到播放列表
  playList.value = [audio.audioUrl || audio.fileUrl || ''];
  playListNames.value = [audio.name];
  
  // 设置当前播放索引为0（因为只有一首）
  currentPlayIndex.value = 0;
}

/** 音频播放事件 */
function onAudioPlay() {
  isPlaying.value = true;
}

/** 音频暂停事件 */
function onAudioPause() {
  isPlaying.value = false;
}

/** 音频播放结束事件 */
function onAudioEnded() {
  isPlaying.value = false;
}

/** 音轨切换事件 */
function onTrackChange(index) {
  // 由于我们使用单曲播放模式，这个回调应该不会被触发
  // 但为了兼容性，我们保留这个方法
  console.log('Track changed to:', index);
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  
  // 删除之前的状态转换逻辑，因为我们已经移除了状态筛选字段
  // 直接调用获取列表方法
  getList();
}

// 统一的数据加载函数
function loadData() {
  // 先设置加载状态，确保UI显示加载中
  loading.value = true;
  
  // 使用setTimeout创建微任务，确保在DOM更新周期之后执行
  setTimeout(() => {
    nextTick(() => {
      // 重置分页为第一页
      queryParams.value.pageNum = 1;
      
      // 强制清空当前数据，触发视图更新
      audioList.value = [];
      
      // 使用新的日期工具函数添加日期范围参数
      const finalParams = addQueryDateRange(queryParams.value, dateRange.value);
      
      // 获取数据
      listAudio(finalParams).then(response => {
        // 强制更新UI
        nextTick(() => {
          audioList.value = response.rows;
          total.value = response.total;
          loading.value = false;
        });
      }).catch(error => {
        loading.value = false;
      });
      
      // 更新电台选项
      getRadioOptions();
    });
  }, 100); // 短暂延迟确保DOM已完全更新
}

// 组件挂载时执行
onMounted(() => {
  initPlayer();
  // 加载电台列表，但不加载音频数据
  getRadioOptions();
});

// 使用watch监听路由变化，确保在标签页切换时重新加载数据
watch(() => route.fullPath, (newValue, oldValue) => {
  if (newValue && (newValue.includes('/listen/audio') || newValue.includes('/system/audio'))) {
    // 确保组件已挂载且路由确实改变了才重新加载
    if (oldValue !== newValue) {
      // 只加载电台列表，不加载音频数据
      getRadioOptions();
      // 如果已选择电台，则加载音频数据
      if (selectedRadioId.value) {
        getList();
      }
    }
  }
}, { immediate: true });

// 使用defineExpose暴露刷新方法给父组件
defineExpose({
  refreshList: loadData
});

/** 格式化时间 */
function formatTime(seconds) {
  if (!seconds) return '00:00';
  seconds = Math.floor(seconds);
  const minutes = Math.floor(seconds / 60);
  seconds = seconds % 60;
  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
}

/** 格式化文件大小 */
function formatFileSize(bytes) {
  if (!bytes) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
</script>

<style scoped>
.mb8 {
  margin-bottom: 8px;
}

.small-padding {
  padding-left: 5px;
  padding-right: 5px;
}

.fixed-width {
  min-width: 120px;
}

/* 表头不换行样式 */
:deep(.el-table__header) th {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  user-select: none;
}

/* 设置表格行高为固定48px */
:deep(.el-table__body) tr {
  height: 48px;
}

.page-header {
  display: none;
}

/* 音频表格样式 */
.audio-table {
  margin-top: 15px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: var(--el-box-shadow-light);
}

/* 音频名称单元格样式 */
.audio-name-cell {
  display: flex;
  align-items: center;
  max-width: 100%;
}

.audio-name-cell span {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: default;
}

/* 时长标签样式 */
.duration-tag {
  display: inline-block;
  padding: 2px 8px;
  background-color: #f0f2f5;
  border-radius: 12px;
  font-family: monospace;
  font-size: 13px;
}

/* 操作按钮样式 */
.operation-buttons {
  display: flex;
  justify-content: center;
  gap: 8px;
}

/* 播放按钮容器 */
.play-button-container {
  display: flex;
  justify-content: center;
}

.play-button {
  transition: all 0.3s;
}

.play-button.playing {
  transform: scale(1.1);
  box-shadow: 0 0 8px rgba(64, 158, 255, 0.4);
}

.play-button.disabled {
  opacity: 0.6;
}

/* 上传组件样式 */
.upload-container {
  width: 100%;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
  background-color: #fafafa;
}

.upload-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.upload-tip {
  margin-left: 12px;
  font-size: 13px;
  color: #909399;
}

.audio-files-table {
  width: 100%;
  margin-top: 15px;
}

.file-table-wrapper {
  border: 1px solid #ebeef5;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.file-table-header {
  display: flex;
  background-color: #f5f7fa;
  padding: 10px 0;
  font-weight: bold;
  border-bottom: 1px solid #ebeef5;
}

.file-table-body {
  max-height: 240px;
  overflow-y: auto;
}

.file-table-row {
  display: flex;
  padding: 10px 0;
  border-bottom: 1px solid #ebeef5;
  transition: background-color 0.2s;
}

.file-table-row:hover {
  background-color: #f5f7fa;
}

.file-table-row:last-child {
  border-bottom: none;
}

.file-name-header, .file-name {
  flex: 2;
  padding: 0 10px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-size-header, .file-size {
  flex: 0.5;
  text-align: center;
}

.file-status-header, .file-status {
  flex: 1;
  text-align: center;
}

.file-operation-header, .file-operation {
  flex: 0.5;
  text-align: center;
}

.status-success {
  color: #67c23a;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.status-error {
  color: #f56c6c;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.status-waiting {
  color: #909399;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.status-link {
  color: #409eff;
  cursor: pointer;
  transition: all 0.2s;
}

.status-link:hover {
  color: #66b1ff;
  text-decoration: underline;
}

.status-link.disabled {
  color: #c0c4cc;
  cursor: not-allowed;
}

.upload-progress {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  padding: 0 10px;
}

.upload-progress span {
  margin-top: 4px;
  font-size: 12px;
  color: #909399;
}

.error-info-icon {
  margin-left: 5px;
  cursor: pointer;
}

.form-tip {
  margin-left: 10px;
  font-size: 12px;
  color: #909399;
}

.upload-audio-item .el-form-item__content {
  display: block;
}

/* 全局播放器容器 */
.global-player-container {
  margin-bottom: 20px;
}

/* 全局播放器样式 */
.global-player {
  position: sticky;
  top: 0;
  z-index: 999;
  background: var(--el-bg-color);
  border-radius: 8px;
  padding: 15px;
  box-shadow: var(--el-box-shadow-light);
  min-height: 80px;
  transition: all 0.3s;
}

.global-player.playing {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #e6f1fc;
  background-color: #f5faff;
}

.empty-player {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60px;
  color: var(--el-text-color-secondary);
  font-size: 14px;
}

.empty-player .el-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

/* 电台选择区域样式 */
.radio-selector-area {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  transition: all 0.3s;
}

.radio-selector-area:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
}

.radio-title h2, 
.radio-selector h2 {
  margin: 0;
  font-size: 18px;
  color: #303133;
  font-weight: 600;
}

.radio-selector h2 {
  color: #909399;
}

/* 添加新的内联播放器样式 */
.audio-player-form-item {
  margin-right: 0;
  vertical-align: middle;
}

.inline-player-container {
  display: inline-flex;
  align-items: center;
  min-width: 304px;
  height: 32px;
  vertical-align: middle;
}

.empty-inline-player {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 32px;
  padding: 0 12px;
  color: var(--el-text-color-secondary);
  font-size: 12px;
  background-color: var(--el-bg-color-overlay);
  border-radius: 4px;
  box-shadow: var(--el-box-shadow-lighter);
}

.empty-inline-player .el-icon {
  font-size: 16px;
  margin-right: 8px;
}

/* 响应式布局调整 */
@media screen and (max-width: 992px) {
  .inline-player-container {
    min-width: 240px;
  }
}

@media screen and (max-width: 768px) {
  .audio-player-form-item {
    width: 100%;
    margin-bottom: 10px;
  }
  
  .inline-player-container {
    width: 100%;
  }
}

/* 查询和播放器容器样式 */
.query-and-player-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  flex-wrap: wrap;
  gap: 10px;
}

.query-form {
  flex: 0 0 auto;
}

.player-wrapper {
  flex: 0 0 304px;
  height: 46px;
}

.empty-player {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 46px;
  padding: 0 12px;
  color: var(--el-text-color-secondary);
  font-size: 12px;
  background-color: var(--el-bg-color-overlay);
  border-radius: 4px;
  box-shadow: var(--el-box-shadow-lighter);
}

.empty-player .el-icon {
  font-size: 16px;
  margin-right: 8px;
}

/* 响应式布局调整 */
@media screen and (max-width: 768px) {
  .query-and-player-container {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .player-wrapper {
    width: 100%;
    flex: 0 0 auto;
  }
}
</style>
