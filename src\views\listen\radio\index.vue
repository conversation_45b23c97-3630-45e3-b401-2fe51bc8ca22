<template>
  <div class="app-container">
    <!-- 固定显示播放器组件 -->
    <div class="global-player">
      <audio-player
        v-if="currentRadio.audioUrl"
        :audio-url="currentRadio.audioUrl"
        :audio-name="currentRadio.audioName"
        @ended="handleAudioEnded"
        @play="handleAudioPlay"
        @pause="handleAudioPause"
      />
      <div v-else class="empty-player">
        <el-icon><Headset /></el-icon>
        <span>点击电台可以播放音频</span>
      </div>
    </div>

    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="电台编号" prop="id">
        <el-input
          v-model="queryParams.id"
          placeholder="请输入电台编号"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="电台名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入电台名称"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="语种分类" prop="language">
        <el-select
          v-model="queryParams.language"
          placeholder="请选择语种分类"
          clearable
          style="width: 240px"
        >
          <el-option label="全部" value="" />
          <el-option label="中文" value="0" />
          <el-option label="英文" value="1" />
          <el-option label="法语" value="2" />
          <el-option label="日语" value="3" />
          <el-option label="德语" value="4" />
          <el-option label="西班牙语" value="5" />
          <el-option label="意大利语" value="6" />
          <el-option label="俄语" value="7" />
          <el-option label="韩语" value="8" />
          <el-option label="葡萄牙语" value="9" />
          <el-option label="阿拉伯语" value="10" />
          <el-option label="希腊语" value="11" />
        </el-select>
      </el-form-item>
      <el-form-item label="电台状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择状态"
          clearable
          style="width: 240px"
        >
          <el-option label="全部" value="" />
          <el-option label="启用" value="1" />
          <el-option label="禁用" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" style="width: 308px">
        <el-date-picker
          v-model="dateRange"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['listen:radio:add']"
        >新增</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="radioList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column label="电台编号" align="center" prop="id" width="80" />
      <el-table-column label="电台名称" prop="name" :show-overflow-tooltip="true" min-width="120" />
      <el-table-column label="简介" prop="description" :show-overflow-tooltip="true" min-width="150" />
      <el-table-column label="封面图" align="center" width="70">
        <template #default="scope">
          <el-image
            v-if="scope.row.cover_url"
            :src="scope.row.cover_url"
            style="width: 40px; height: 40px"
            :preview-src-list="[scope.row.cover_url]"
          />
          <span v-else>无封面</span>
        </template>
      </el-table-column>
      <el-table-column label="语种分类" align="center" width="100">
        <template #default="scope">
          <dict-tag :options="[
            { label: '中文', value: '0', elTagType: 'primary' },
            { label: '英文', value: '1', elTagType: 'info' }
          ]" :value="scope.row.language" />
        </template>
      </el-table-column>
      <el-table-column label="推荐位排序" align="center" prop="order" width="90" />
      <el-table-column label="电台状态" align="center" width="80">
        <template #default="scope">
          <dict-tag :options="[
            { label: '启用', value: '1', elTagType: 'success' },
            { label: '禁用', value: '0', elTagType: 'danger' }
          ]" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="音频数量" align="center" prop="audio_num" width="80" />
      <el-table-column label="电台播放量" align="center" prop="play_times" width="90" />
      <el-table-column label="创建时间" align="center" prop="created" width="150">
        <template #default="scope">
          <span>{{ parseTime(scope.row.created) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="试听" align="center" width="60">
        <template #default="scope">
          <el-tooltip :content="currentRadio.radioId === scope.row.id && isPlaying ? '暂停' : '播放'" placement="top">
            <el-button 
              circle 
              :icon="currentRadio.radioId === scope.row.id && isPlaying ? 'VideoPause' : 'VideoPlay'" 
              @click="playRadio(scope.row)"
              size="small"
            />
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-tooltip content="查看音频" placement="top">
            <el-button link type="primary" icon="View" @click="handleViewAudios(scope.row)" v-hasPermi="['listen:radio:query']"></el-button>
          </el-tooltip>
          <el-tooltip content="修改" placement="top">
            <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['listen:radio:edit']"></el-button>
          </el-tooltip>
          <el-tooltip :content="scope.row.status === '0' ? '启用' : '禁用'" placement="top">
            <el-button
              link
              type="primary"
              :icon="scope.row.status === '0' ? 'Check' : 'Close'"
              v-if="scope.row.status === '0' || scope.row.status === '1'"
              @click="handleStatusChange(scope.row, scope.row.status === '0' ? '1' : '0')"
              v-hasPermi="['listen:radio:edit']"
            ></el-button>
          </el-tooltip>
          <el-tooltip content="删除" placement="top">
            <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['listen:radio:remove']"></el-button>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加/修改电台对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="radioFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="电台名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入电台名称" />
        </el-form-item>
        <el-form-item label="简介" prop="description">
          <el-input v-model="form.description" placeholder="请输入简介" />
        </el-form-item>
        <el-form-item label="上传封面" prop="coverFile">
          <div class="upload-wrapper">
            <el-upload
              class="cover-uploader"
              action="#"
              :http-request="uploadCoverRequest"
              :show-file-list="false"
              :before-upload="beforeCoverUpload"
            >
              <img v-if="form.cover_url" :src="form.cover_url" class="cover-image" />
              <div v-else class="cover-upload-placeholder">
                <el-icon><Plus /></el-icon>
                <div>上传封面</div>
              </div>
            </el-upload>
            <div class="upload-tip">只支持 .jpg 格式</div>
          </div>
        </el-form-item>
        <el-form-item label="语种分类" prop="language">
          <el-select v-model="form.language" placeholder="请选择语种分类" style="width: 100%">
            <el-option label="中文" value="0" />
            <el-option label="英文" value="1" />
            <el-option label="法语" value="2" />
            <el-option label="日语" value="3" />
            <el-option label="德语" value="4" />
            <el-option label="西班牙语" value="5" />
            <el-option label="意大利语" value="6" />
            <el-option label="俄语" value="7" />
            <el-option label="韩语" value="8" />
            <el-option label="葡萄牙语" value="9" />
            <el-option label="阿拉伯语" value="10" />
            <el-option label="希腊语" value="11" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio label="1">启用</el-radio>
            <el-radio label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Radio">
import { listRadio, getRadio, getRadioAudios, addRadio, updateRadio, uploadCover, delRadio, enableRadio, disableRadio } from "@/api/listen/radio";
import AudioPlayer from "@/components/AudioPlayer/index.vue";
import { onMounted, nextTick } from 'vue';
import { parseTime } from '@/utils/ruoyi';
import DictTag from '@/components/DictTag/index.vue';

const { proxy } = getCurrentInstance();
const router = useRouter();

const radioList = ref([]);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const dateRange = ref([]);
const title = ref("");
const open = ref(false);

const data = reactive({
  form: {
    id: undefined,
    name: '',
    description: '',
    cover_url: '',
    cover_file_id: undefined,
    coverFile: undefined,
    language: '0',
    status: '1',
    order: 0,
    play_stram_url: ''
  },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    id: undefined,
    name: undefined,
    language: undefined,
    status: undefined
  },
  rules: {
    name: [
      { required: true, message: "电台名称不能为空", trigger: "blur" }
    ],
    description: [
      { required: true, message: "简介不能为空", trigger: "blur" }
    ],
    language: [
      { required: true, message: "语种分类不能为空", trigger: "change" }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

// 播放相关
const currentRadio = ref({
  radioId: null,
  radioName: '',
  audioUrl: '',
  audioName: ''
});
const isPlaying = ref(false);
const currentAudioIndex = ref(0);
const currentRadioAudios = ref([]);

/** 初始化播放器 */
function initPlayer() {
  currentRadio.value = {
    radioId: null,
    radioName: '',
    audioUrl: '',
    audioName: ''
  };
  isPlaying.value = false;
  currentAudioIndex.value = 0;
  currentRadioAudios.value = [];
}

/** 查询电台列表 */
function getList() {
  loading.value = true;
  listRadio(proxy.addDateRange(queryParams.value, dateRange.value)).then(response => {
    radioList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = [];
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  form.value = {
    id: undefined,
    name: '',
    description: '',
    cover_url: '',
    cover_file_id: undefined,
    coverFile: undefined,
    language: '0',
    status: '1',
    order: 0,
    play_stram_url: ''
  };
  proxy.resetForm("radioFormRef");
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加电台";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const radioId = row.id || ids.value;
  getRadio(radioId).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改电台";
  });
}

/** 状态修改 */
function handleStatusChange(row, status) {
  const text = status === "1" ? "启用" : "禁用";
  proxy.$modal.confirm('确认要"' + text + '""' + row.name + '"电台吗?').then(function() {
    const radioId = row.id;
    // 调用状态修改API
    if (status === "1") {
      // 启用
      return enableRadio(radioId);
    } else {
      // 禁用
      return disableRadio(radioId);
    }
  }).then(() => {
    proxy.$modal.msgSuccess(text + "成功");
    getList();
  }).catch(() => {});
}

/** 删除按钮操作 */
function handleDelete(row) {
  const radioIds = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除电台编号为"' + radioIds + '"的数据项?').then(function() {
    return delRadio(radioIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["radioFormRef"].validate(valid => {
    if (valid) {
      // 准备提交的数据，移除不需要的字段
      const submitData = {
        ...form.value,
        coverFile: undefined // 移除文件对象，不需要提交到后端
      };

      // 确保封面地址和路径被正确提交
      if (!submitData.cover_url) {
        proxy.$modal.msgWarning("请上传电台封面");
        return;
      }

      if (submitData.id != undefined) {
        updateRadio(submitData).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addRadio(submitData).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 封面上传前的校验 */
function beforeCoverUpload(file) {
  const isJPG = file.type === 'image/jpeg';
  const isLt2M = file.size / 1024 / 1024 < 2;

  if (!isJPG) {
    proxy.$modal.msgError('上传封面图片只能是 JPG 格式!');
    return false;
  }
  if (!isLt2M) {
    proxy.$modal.msgError('上传封面图片大小不能超过 2MB!');
    return false;
  }
  return true;
}

/** 自定义封面上传 */
function uploadCoverRequest(options) {
  const formData = new FormData();
  formData.append('file', options.file);

  // 模拟上传成功
  setTimeout(() => {
    form.value.cover_url = URL.createObjectURL(options.file);
    form.value.cover_file_id = options.file.name;
    form.value.coverFile = options.file;
    proxy.$modal.msgSuccess("封面上传成功");
  }, 1000);
}

/** 查看电台音频 */
function handleViewAudios(row) {
  router.push({
    path: '/listen/audio',
    query: {
      radioId: row.id,
      radioName: row.name
    }
  });
}

/** 选择条数  */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

// 播放电台音频
function playRadio(radio) {
  // 如果是当前正在播放的电台，则切换播放状态
  if (currentRadio.value.radioId === radio.id && currentRadio.value.audioUrl) {
    isPlaying.value = !isPlaying.value;
    return;
  }
  
  // 显示加载状态
  loading.value = true;
  
  // 获取电台对应的音频列表
  getRadioAudios(radio.id).then(response => {
    const audioList = response.data || [];
    
    if (audioList.length > 0) {
      currentRadioAudios.value = audioList;
      currentAudioIndex.value = 0;
      
      const firstAudio = audioList[0];
      // 设置当前播放的电台和音频
      currentRadio.value = {
        radioId: radio.id,
        radioName: radio.name,
        audioUrl: firstAudio.audioUrl || firstAudio.file_url,
        audioName: `${radio.name} - ${firstAudio.audioName || firstAudio.name}`
      };
      
      isPlaying.value = true;
    } else {
      proxy.$modal.msgInfo("该电台暂无音频");
    }
    
    loading.value = false;
  }).catch(() => {
    loading.value = false;
    proxy.$modal.msgError("获取音频失败");
  });
}

// 音频播放结束
function handleAudioEnded() {
  // 播放下一首
  if (currentRadioAudios.value.length > 0) {
    currentAudioIndex.value = (currentAudioIndex.value + 1) % currentRadioAudios.value.length;
    const nextAudio = currentRadioAudios.value[currentAudioIndex.value];
    
    currentRadio.value = {
      ...currentRadio.value,
      audioUrl: nextAudio.audioUrl,
      audioName: `${currentRadio.value.radioName} - ${nextAudio.audioName}`
    };
  } else {
    isPlaying.value = false;
  }
}

// 音频播放事件
function handleAudioPlay() {
  isPlaying.value = true;
}

// 音频暂停事件
function handleAudioPause() {
  isPlaying.value = false;
}

// 组件挂载时执行
onMounted(() => {
  initPlayer();
  // 确保页面加载时立即获取数据
  nextTick(() => {
    getList();
  });
});
</script>

<style scoped>
.mb8 {
  margin-bottom: 8px;
}

.small-padding {
  padding-left: 5px;
  padding-right: 5px;
}

.fixed-width {
  min-width: 120px;
}

.cover-uploader {
  width: 150px;
  height: 150px;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fafafa;
}

.cover-uploader:hover {
  border-color: #409EFF;
}

.cover-upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  color: #8c939d;
}

.cover-image {
  max-width: 100%;
  max-height: 100%;
  display: block;
}

.upload-wrapper {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.upload-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 8px;
  display: block;
  text-align: left;
}

/* 全局播放器样式 */
.global-player {
  position: sticky;
  top: 0;
  z-index: 999;
  margin-bottom: 20px;
  background: var(--el-bg-color);
  border-radius: 4px;
  padding: 10px;
  box-shadow: var(--el-box-shadow-light);
  min-height: 80px;
}

.empty-player {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60px;
  color: var(--el-text-color-secondary);
  font-size: 14px;
}

.empty-player .el-icon {
  font-size: 24px;
  margin-bottom: 8px;
}
</style>
