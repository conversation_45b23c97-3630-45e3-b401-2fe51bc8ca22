DROP TABLE IF EXISTS `uploaded_file`;
CREATE TABLE `uploaded_file` (
  `id` bigint(20) NOT NULL COMMENT 'ID',
  `file_name` varchar(255) NOT NULL COMMENT '文件名',
  `file_path` varchar(255) NOT NULL COMMENT '存储路径 /home/<USER>/math/',
  `file_size` int(11) NOT NULL DEFAULT 0 COMMENT '文件大小',
  `file_type` tinyint(4) NOT NULL COMMENT '文件类型',
  `md5_hash` char(32) DEFAULT NULL COMMENT 'md5文件去重',
  `deleted` tinyint(4) NOT NULL DEFAULT 0 COMMENT '状态：0：未删除 1：已删除',
  `created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE_KEY `idx_uploaded_file_md5_hash` (`md5_hash`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文件上传表';

-- 电台表
DROP TABLE IF EXISTS `radio_station`;
CREATE TABLE `radio_station` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `name` VARCHAR(255) NOT NULL COMMENT '电台名称',
  `description` VARCHAR(255) NOT NULL COMMENT '简介',
  `cover_url` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '封面图片URL',
  `cover_file_id` bigint(20) DEFAULT NULL COMMENT '封面对应的文件id',
  `language` tinyint(4) NOT NULL DEFAULT 0 COMMENT '电台语言 0:中文 1:英文',
  `status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '电台状态 0:禁用 1:启用',
  `playing` tinyint(4) NOT NULL DEFAULT 0 COMMENT '电台播放状态 0:未播放  1:播放',
  `play_stram_url` VARCHAR(255) NOT NULL COMMENT '电台流地址',
  `order` int(32) NOT NULL DEFAULT 0 COMMENT '电台顺序',
  `audio_num` int(32) NOT NULL DEFAULT 0 COMMENT '电台音频数量',
  `play_times` int(32) NOT NULL DEFAULT 0 COMMENT '电台播放次数',
  `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除 0:未删除 1:已删除',
  `created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='电台表';

-- 音频表
DROP TABLE IF EXISTS `audio`;
CREATE TABLE `audio` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `name` VARCHAR(255) NOT NULL COMMENT '音频名称',
  `file_id` bigint(20)  COMMENT '音频文件id',
  `file_url` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '音频文件url',
  `seconds` int(32) NOT NULL DEFAULT 0 COMMENT '音频时长 单位：秒',
  `status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '音频可用状态 0:禁用 1:启用',
  `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除 0:未删除 1:已删除',
  `created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='音频表';

-- 电台音频表
DROP TABLE IF EXISTS `radio_station_audio`;
CREATE TABLE `radio_station_audio` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `radio_station_id` bigint(20) NOT NULL COMMENT '电台id',
  `audio_id` bigint(20) NOT NULL COMMENT '音频id',
  `order` int(32) NOT NULL DEFAULT 0 COMMENT '音频顺序 越小排名越前',
  `status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否播放 0:不播放 1:播放',
  `created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  KEY `idx_radio_station_id_status` (`radio_station_id`,`status`),
  PRIMARY KEY (`id`)
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='电台音频表';

-- 电台音频播放记录表
DROP TABLE IF EXISTS `radio_station_audio_play_record`;
CREATE TABLE `radio_station_audio_play_record` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `radio_station_id` bigint(20) NOT NULL COMMENT '电台id',
  `audio_id` bigint(20) NOT NULL COMMENT '音频id',
  `created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  KEY `idx_radio_station_audio_radio_station_id` (`radio_station_id`),
  PRIMARY KEY (`id`)
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='电台音频播放记录表';

-- 用户电台播放记录表
DROP TABLE IF EXISTS `radio_station_user_play_record`;
CREATE TABLE `radio_station_user_play_record` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `user_id` bigint(20) NOT NULL COMMENT '用户id',
  `radio_station_id` bigint(20) NOT NULL COMMENT '电台id',
  `seconds` int(32) NOT NULL DEFAULT 0 COMMENT '播放时长 单位：秒',
  `bandwidth_up` bigint(20) NOT NULL DEFAULT 0 COMMENT '上行流量 单位：字节',
  `bandwidth_black` bigint(20) NOT NULL DEFAULT 0 COMMENT '下行流量 单位：字节',
  `created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id`(`user_id`),
  KEY `idx_radio_station_id`(`radio_station_id`)
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='电台流播放记录表';


CREATE TABLE `user` (
  `id` bigint NOT NULL COMMENT '主键',
  `nickname` varchar(255) NOT NULL DEFAULT '' COMMENT '昵称',
  `phone` varchar(20) NOT NULL COMMENT '手机号',
  `password` varchar(1000) NOT NULL COMMENT '密码',
  `salt` varchar(255) NOT NULL COMMENT '盐值',
  `avatar_file_id` bigint NOT NULL DEFAULT '0' COMMENT '头像对应的文件id',
  `avatar_url` varchar(1024) DEFAULT '' COMMENT '头像',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态',
  `register_type` tinyint NOT NULL DEFAULT '0' COMMENT '注册类型，0=phone',
  `created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户表';