import request from '@/utils/request'
import { getMockRadioList, getMockRadio, getMockRadioAudios } from './mock'

// 查询电台列表
export function listRadio(query) {
  // 使用mock数据
  return getMockRadioList(query);
  
  // 实际接口
  /*
  return request({
    url: '/listen/radio/list',
    method: 'get',
    params: query
  })
  */
}

// 查询电台详细
export function getRadio(radioId) {
  // 使用mock数据
  return getMockRadio(radioId);
  
  // 实际接口
  /*
  return request({
    url: '/listen/radio/' + radioId,
    method: 'get'
  })
  */
}

// 获取电台音频列表
export function getRadioAudios(radioId) {
  // 使用mock数据
  return getMockRadioAudios(radioId);
  
  // 实际接口
  /*
  return request({
    url: '/listen/radio/audios/' + radioId,
    method: 'get'
  })
  */
}

// 新增电台
export function addRadio(data) {
  return request({
    url: '/listen/radio',
    method: 'post',
    data: data
  })
}

// 修改电台
export function updateRadio(data) {
  return request({
    url: '/listen/radio',
    method: 'put',
    data: data
  })
}

// 删除电台
export function delRadio(radioId) {
  return request({
    url: '/listen/radio/' + radioId,
    method: 'delete'
  })
}

// 导出电台
export function exportRadio(query) {
  return request({
    url: '/listen/radio/export',
    method: 'get',
    params: query
  })
}

// 上传封面
export function uploadCover(data) {
  return request({
    url: '/listen/radio/upload',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 启用电台
export function enableRadio(radioId) {
  return request({
    url: '/listen/radio/enable/' + radioId,
    method: 'put'
  })
}

// 禁用电台
export function disableRadio(radioId) {
  return request({
    url: '/listen/radio/disable/' + radioId,
    method: 'put'
  })
}

// 上传音频文件
export function uploadAudio(data) {
  return request({
    url: '/listen/radio/upload/audio',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
