import request from '@/utils/request'
import { getMockAudioList, getMockAudio } from './mock'

// 查询音频列表
export function listAudio(query) {
  // 使用mock数据
  return getMockAudioList(query);
  
  // 实际接口
  /*
  return request({
    url: '/listen/audio/list',
    method: 'get',
    params: query
  })
  */
}

// 查询音频详细
export function getAudio(audioId) {
  // 使用mock数据
  return getMockAudio(audioId);
  
  // 实际接口
  /*
  return request({
    url: '/listen/audio/' + audioId,
    method: 'get'
  })
  */
}

// 新增音频
export function addAudio(data) {
  return request({
    url: '/listen/audio',
    method: 'post',
    data: data
  })
}

// 修改音频
export function updateAudio(data) {
  return request({
    url: '/listen/audio',
    method: 'put',
    data: data
  })
}

// 删除音频
export function delAudio(audioId) {
  return request({
    url: '/listen/audio/' + audioId,
    method: 'delete'
  })
}

// 导出音频
export function exportAudio(query) {
  return request({
    url: '/listen/audio/export',
    method: 'get',
    params: query
  })
}

// 上传音频文件
export function uploadAudioFile(data, onUploadProgress) {
  return request({
    url: '/listen/audio/upload',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    onUploadProgress
  })
}

// 启用音频
export function enableAudio(audioId) {
  return request({
    url: '/listen/audio/enable/' + audioId,
    method: 'put'
  })
}

// 禁用音频
export function disableAudio(audioId) {
  return request({
    url: '/listen/audio/disable/' + audioId,
    method: 'put'
  })
}

// 获取音频文件信息
export function getAudioFileInfo(fileId) {
  return request({
    url: '/listen/audio/file/' + fileId,
    method: 'get'
  })
}

// 删除音频文件
export function delAudioFile(fileId) {
  return request({
    url: '/listen/audio/file/' + fileId,
    method: 'delete'
  })
}

// 获取电台列表（用于下拉选择）
export function getRadioOptions() {
  return request({
    url: '/listen/radio/options',
    method: 'get'
  })
}
