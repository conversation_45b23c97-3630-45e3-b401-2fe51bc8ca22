<template>
  <div class="audio-player" :class="{ 'is-playing': isPlaying }">
    <div class="player-content">
      <div class="player-controls">
        <el-button 
          :icon="isPlaying ? 'VideoPause' : 'VideoPlay'" 
          circle 
          class="control-btn play-btn"
          @click="togglePlay"
        />
        <div class="progress-container">
          <div class="time-info">{{ formatTime(currentTime) }}</div>
          <el-slider 
            v-model="progress" 
            :max="100"
            class="progress-slider"
            @change="handleProgressChange"
          />
          <div class="time-info">{{ formatTime(duration) }}</div>
        </div>
        <div class="volume-control">
          <el-button 
            :icon="volume === 0 ? 'Mute' : 'Microphone'" 
            circle 
            class="control-btn volume-btn"
            @click="toggleMute"
          />
          <el-slider 
            v-model="volume" 
            :max="100"
            class="volume-slider"
            @change="handleVolumeChange"
          />
        </div>
      </div>
      <div class="audio-info">
        <span class="audio-name" :title="audioName">{{ audioName }}</span>
      </div>
    </div>
    <audio
      ref="audioEl"
      :src="audioUrl"
      @timeupdate="onTimeUpdate"
      @loadedmetadata="onLoadedMetadata"
      @ended="onEnded"
      @error="onError"
      @play="onPlay"
      @pause="onPause"
    ></audio>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onBeforeUnmount } from 'vue'

const props = defineProps({
  audioUrl: {
    type: String,
    required: true
  },
  audioName: {
    type: String,
    default: '未知音频'
  },
  autoplay: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['play', 'pause', 'ended', 'error', 'timeupdate'])

const audioEl = ref(null)
const isPlaying = ref(false)
const currentTime = ref(0)
const duration = ref(0)
const volume = ref(100)
const lastVolume = ref(100)

const progress = computed({
  get: () => (duration.value ? (currentTime.value / duration.value) * 100 : 0),
  set: (val) => handleProgressChange(val)
})

// 播放控制
const togglePlay = () => {
  if (isPlaying.value) {
    pause()
  } else {
    play()
  }
}

const play = async () => {
  try {
    await audioEl.value.play()
  } catch (error) {
    console.error('播放失败:', error)
    emit('error', error)
  }
}

const pause = () => {
  audioEl.value.pause()
}

// 音量控制
const toggleMute = () => {
  if (volume.value > 0) {
    lastVolume.value = volume.value
    volume.value = 0
  } else {
    volume.value = lastVolume.value
  }
}

const handleVolumeChange = (val) => {
  volume.value = val
  if (audioEl.value) {
    audioEl.value.volume = val / 100
  }
}

// 进度控制
const handleProgressChange = (val) => {
  if (audioEl.value && duration.value) {
    const time = (val / 100) * duration.value
    audioEl.value.currentTime = time
    currentTime.value = time
  }
}

// 事件处理
const onTimeUpdate = () => {
  if (audioEl.value) {
    currentTime.value = audioEl.value.currentTime
    emit('timeupdate', currentTime.value)
  }
}

const onLoadedMetadata = () => {
  if (audioEl.value) {
    duration.value = audioEl.value.duration
    if (props.autoplay) {
      play()
    }
  }
}

const onPlay = () => {
  isPlaying.value = true
  emit('play')
}

const onPause = () => {
  isPlaying.value = false
  emit('pause')
}

const onEnded = () => {
  isPlaying.value = false
  emit('ended')
}

const onError = (error) => {
  isPlaying.value = false
  emit('error', error)
}

// 工具函数
const formatTime = (time) => {
  if (!time) return '00:00'
  const minutes = Math.floor(time / 60)
  const seconds = Math.floor(time % 60)
  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
}

// 组件生命周期
onMounted(() => {
  if (audioEl.value) {
    audioEl.value.volume = volume.value / 100
  }
})

onBeforeUnmount(() => {
  if (audioEl.value && !audioEl.value.paused) {
    audioEl.value.pause()
  }
})

// 监听 URL 变化
watch(() => props.audioUrl, () => {
  if (isPlaying.value) {
    pause()
  }
  currentTime.value = 0
  duration.value = 0
  if (props.autoplay) {
    nextTick(() => {
      play()
    })
  }
})
</script>

<style scoped>
.audio-player {
  background: var(--el-bg-color-overlay);
  border-radius: 8px;
  padding: 12px;
  box-shadow: var(--el-box-shadow-light);
  width: 100%;
}

.player-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.player-controls {
  display: flex;
  align-items: center;
  gap: 16px;
}

.control-btn {
  flex-shrink: 0;
}

.progress-container {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-slider {
  flex: 1;
}

.time-info {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  width: 45px;
  text-align: center;
}

.volume-control {
  display: flex;
  align-items: center;
  gap: 8px;
}

.volume-slider {
  width: 80px;
}

.audio-info {
  padding: 4px 0;
}

.audio-name {
  font-size: 14px;
  color: var(--el-text-color-primary);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
}

.play-btn {
  position: relative;
}

.is-playing .play-btn::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: var(--el-color-primary-light-9);
  animation: ripple 1s infinite;
  z-index: -1;
}

@keyframes ripple {
  0% {
    transform: scale(1);
    opacity: 0.4;
  }
  100% {
    transform: scale(1.5);
    opacity: 0;
  }
}

/* 响应式布局 */
@media screen and (max-width: 768px) {
  .volume-slider {
    display: none;
  }
  
  .time-info {
    width: 40px;
    font-size: 11px;
  }
}
</style> 