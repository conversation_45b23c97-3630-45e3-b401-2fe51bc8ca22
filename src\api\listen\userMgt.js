import request from '@/utils/request'
import { parseStrEmpty } from "@/utils/ruoyi";

// 查询用户列表
export function listUser(query) {
  return request({
    url: '/system/userMgt/list',
    method: 'get',
    params: query
  })
}

// 查询用户详细
export function getUser(userId) {
  return request({
    url: '/system/userMgt/' + parseStrEmpty(userId),
    method: 'get'
  })
}

// 新增用户
export function addUser(data) {
  return request({
    url: '/system/userMgt',
    method: 'post',
    data: data
  })
}

// 修改用户
export function updateUser(data) {
  return request({
    url: '/system/userMgt',
    method: 'put',
    data: data
  })
}

// 删除用户
export function delUser(userId) {
  return request({
    url: '/system/userMgt/' + userId,
    method: 'delete'
  })
}

// 用户状态修改
export function changeUserStatus(userId, status) {
  const data = {
    userId,
    status
  }
  return request({
    url: '/system/userMgt/changeStatus',
    method: 'put',
    data: data
  })
}

// Mock数据
const mockUserList = [
  {
    id: 1,
    nickname: '管理员',
    phone: '13800138000',
    password: 'encrypted_password_1',
    salt: 'salt_value_1',
    avatar_file_id: 101,
    avatar_url: 'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif',
    status: 1, // 1=启用，0=禁用
    register_type: 0,
    created: '2023-01-01 00:00:00',
    updated: '2023-01-01 00:00:00'
  },
  {
    id: 2,
    nickname: '测试用户',
    phone: '13900139000',
    password: 'encrypted_password_2',
    salt: 'salt_value_2',
    avatar_file_id: 102,
    avatar_url: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
    status: 1,
    register_type: 0,
    created: '2023-02-01 00:00:00',
    updated: '2023-02-01 00:00:00'
  },
  {
    id: 3,
    nickname: '张三',
    phone: '13700137000',
    password: 'encrypted_password_3',
    salt: 'salt_value_3',
    avatar_file_id: 103,
    avatar_url: '',
    status: 1,
    register_type: 0,
    created: '2023-03-01 00:00:00',
    updated: '2023-03-01 00:00:00'
  },
  {
    id: 4,
    nickname: '李四',
    phone: '13600136000',
    password: 'encrypted_password_4',
    salt: 'salt_value_4',
    avatar_file_id: 104,
    avatar_url: '',
    status: 0,
    register_type: 0,
    created: '2023-04-01 00:00:00',
    updated: '2023-04-01 00:00:00'
  },
  {
    id: 5,
    nickname: '王五',
    phone: '13500135000',
    password: 'encrypted_password_5',
    salt: 'salt_value_5',
    avatar_file_id: 105,
    avatar_url: '',
    status: 1,
    register_type: 0,
    created: '2023-05-01 00:00:00',
    updated: '2023-05-01 00:00:00'
  },
  {
    id: 6,
    nickname: '赵六',
    phone: '13400134000',
    password: 'encrypted_password_6',
    salt: 'salt_value_6',
    avatar_file_id: 106,
    avatar_url: '',
    status: 0,
    register_type: 0,
    created: '2023-06-01 00:00:00',
    updated: '2023-06-01 00:00:00'
  },
  {
    id: 7,
    nickname: '钱七',
    phone: '13300133000',
    password: 'encrypted_password_7',
    salt: 'salt_value_7',
    avatar_file_id: 107,
    avatar_url: '',
    status: 1,
    register_type: 0,
    created: '2023-07-01 00:00:00',
    updated: '2023-07-01 00:00:00'
  },
  {
    id: 8,
    nickname: '孙八',
    phone: '13200132000',
    password: 'encrypted_password_8',
    salt: 'salt_value_8',
    avatar_file_id: 108,
    avatar_url: '',
    status: 1,
    register_type: 0,
    created: '2023-08-01 00:00:00',
    updated: '2023-08-01 00:00:00'
  },
  {
    id: 9,
    nickname: '周九',
    phone: '13100131000',
    password: 'encrypted_password_9',
    salt: 'salt_value_9',
    avatar_file_id: 109,
    avatar_url: '',
    status: 0,
    register_type: 0,
    created: '2023-09-01 00:00:00',
    updated: '2023-09-01 00:00:00'
  },
  {
    id: 10,
    nickname: '吴十',
    phone: '13000130000',
    password: 'encrypted_password_10',
    salt: 'salt_value_10',
    avatar_file_id: 110,
    avatar_url: '',
    status: 1,
    register_type: 0,
    created: '2023-10-01 00:00:00',
    updated: '2023-10-01 00:00:00'
  },
  {
    id: 11,
    nickname: '郑十一',
    phone: '12900129000',
    password: 'encrypted_password_11',
    salt: 'salt_value_11',
    avatar_file_id: 111,
    avatar_url: '',
    status: 1,
    register_type: 0,
    created: '2023-11-01 00:00:00',
    updated: '2023-11-01 00:00:00'
  }
];

// 模拟分页查询
function mockPagination(list, query) {
  const { pageNum = 1, pageSize = 10 } = query;
  const start = (pageNum - 1) * pageSize;
  const end = start + pageSize;
  const total = list.length;
  const rows = list.slice(start, end);
  
  return {
    rows,
    total,
    pageNum: Number(pageNum),
    pageSize: Number(pageSize),
    pages: Math.ceil(total / pageSize)
  };
}

// 模拟过滤
function mockFilter(list, query) {
  let result = [...list];
  
  // 根据查询条件过滤
  Object.keys(query).forEach(key => {
    if (query[key] && key !== 'pageNum' && key !== 'pageSize' && key !== 'params') {
      result = result.filter(item => {
        if (key === 'id') {
          return String(item[key]) === String(query[key]);
        }
        if (key === 'nickname') {
          return item[key].includes(query[key]);
        }
        if (key === 'phone') {
          return item[key].includes(query[key]);
        }
        if (key === 'status') {
          return String(item[key]) === String(query[key]);
        }
        return item[key] === query[key];
      });
    }
  });
  
  // 处理时间范围查询
  if (query.params && query.params.beginTime && query.params.endTime) {
    result = result.filter(item => {
      const createTime = new Date(item.created).getTime();
      const beginTime = new Date(query.params.beginTime + ' 00:00:00').getTime();
      const endTime = new Date(query.params.endTime + ' 23:59:59').getTime();
      return createTime >= beginTime && createTime <= endTime;
    });
  }
  
  return result;
}

// 导出模拟数据接口
export function getMockUserList(query = {}) {
  console.log('Mock User List Query:', query);
  const filteredList = mockFilter(mockUserList, query);
  const result = mockPagination(filteredList, query);
  console.log('Mock User List Result:', result);
  return Promise.resolve(result);
}

export function getMockUser(userId) {
  const user = mockUserList.find(item => item.id === Number(userId));
  return Promise.resolve({ data: user || null });
}

// 模拟添加用户
export function mockAddUser(data) {
  const newUser = {
    ...data,
    id: mockUserList.length + 1,
    created: new Date().toISOString().replace('T', ' ').substring(0, 19),
    updated: new Date().toISOString().replace('T', ' ').substring(0, 19)
  };
  mockUserList.push(newUser);
  return Promise.resolve({ code: 200, msg: '添加成功' });
}

// 模拟更新用户
export function mockUpdateUser(data) {
  const index = mockUserList.findIndex(item => item.id === data.id);
  if (index !== -1) {
    mockUserList[index] = {
      ...mockUserList[index],
      ...data,
      updated: new Date().toISOString().replace('T', ' ').substring(0, 19)
    };
    return Promise.resolve({ code: 200, msg: '修改成功' });
  }
  return Promise.reject(new Error('用户不存在'));
}

// 模拟删除用户
export function mockDeleteUser(userId) {
  const index = mockUserList.findIndex(item => item.id === Number(userId));
  if (index !== -1) {
    mockUserList.splice(index, 1);
    return Promise.resolve({ code: 200, msg: '删除成功' });
  }
  return Promise.reject(new Error('用户不存在'));
}

// 模拟修改用户状态
export function mockChangeUserStatus(userId, status) {
  const user = mockUserList.find(item => item.id === Number(userId));
  if (user) {
    user.status = Number(status);
    user.updated = new Date().toISOString().replace('T', ' ').substring(0, 19);
    return Promise.resolve({ code: 200, msg: '状态修改成功' });
  }
  return Promise.reject(new Error('用户不存在'));
} 